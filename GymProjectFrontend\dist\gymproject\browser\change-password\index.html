<!DOCTYPE html><html lang="en"><head>
  <meta charset="utf-8">
  <title>Spor Salonu QR</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&amp;display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="styles.css"><link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&amp;display=swap" as="style"><link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style"><style ng-app-id="ng">

.app-initializing[_ngcontent-ng-c4172328733] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-color);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
.initializing-spinner[_ngcontent-ng-c4172328733] {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.spinner-circle[_ngcontent-ng-c4172328733] {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid transparent;
  border-top-color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  animation: _ngcontent-ng-c4172328733_spin 1.5s linear infinite;
}
.dumbbell[_ngcontent-ng-c4172328733] {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: _ngcontent-ng-c4172328733_lift 2s ease-in-out infinite;
}
.weight[_ngcontent-ng-c4172328733] {
  background:
    linear-gradient(
      135deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(67, 97, 238, 0.5);
}
.inner-weight[_ngcontent-ng-c4172328733] {
  width: 50%;
  height: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}
.weight.left[_ngcontent-ng-c4172328733] {
  animation: _ngcontent-ng-c4172328733_pulse-left 2s ease-in-out infinite;
}
.weight.right[_ngcontent-ng-c4172328733] {
  animation: _ngcontent-ng-c4172328733_pulse-right 2s ease-in-out infinite;
}
.handle[_ngcontent-ng-c4172328733] {
  height: 8px;
  width: 50px;
  background:
    linear-gradient(
      90deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(67, 97, 238, 0.5);
}
@keyframes _ngcontent-ng-c4172328733_spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes _ngcontent-ng-c4172328733_lift {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}
@keyframes _ngcontent-ng-c4172328733_pulse-left {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
@keyframes _ngcontent-ng-c4172328733_pulse-right {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
.app-container[_ngcontent-ng-c4172328733] {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  opacity: 1;
  visibility: visible;
}
.app-container.initializing[_ngcontent-ng-c4172328733] {
  opacity: 0;
  visibility: hidden;
}
.main-content[_ngcontent-ng-c4172328733] {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}
.content-area[_ngcontent-ng-c4172328733] {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  transition: all 0.3s ease;
}
.mobile-header[_ngcontent-ng-c4172328733] {
  display: none;
  padding: 15px;
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px var(--shadow-color);
  z-index: 100;
}
.mobile-title[_ngcontent-ng-c4172328733] {
  font-size: 18px;
  font-weight: 600;
}
.sidebar-toggle[_ngcontent-ng-c4172328733], 
.theme-toggle[_ngcontent-ng-c4172328733] {
  background: none;
  border: none;
  color: var(--sidebar-text);
  font-size: 18px;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.sidebar-toggle[_ngcontent-ng-c4172328733]:hover, 
.theme-toggle[_ngcontent-ng-c4172328733]:hover {
  background-color: var(--sidebar-hover);
}
@media (max-width: 991.98px) {
  .mobile-header[_ngcontent-ng-c4172328733] {
    display: flex;
  }
  .app-container.sidebar-collapsed[_ngcontent-ng-c4172328733]   .main-content[_ngcontent-ng-c4172328733] {
    margin-left: 0;
  }
}
body.initializing[_ngcontent-ng-c4172328733] {
  overflow: hidden;
}
/*# sourceMappingURL=/app.component.css.map */</style><style ng-app-id="ng">

.main-content[_ngcontent-ng-c2664970643] {
  padding: 30px 0;
  min-height: calc(100vh - 70px);
}
.card[_ngcontent-ng-c2664970643] {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background-color: var(--card-bg-color);
  margin-bottom: 30px;
  overflow: hidden;
}
.card-header[_ngcontent-ng-c2664970643] {
  background-color: var(--card-bg-color);
  border-bottom: 1px solid var(--border-color);
  padding: 20px 25px;
}
.card-title[_ngcontent-ng-c2664970643] {
  margin: 0;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}
.card-title[_ngcontent-ng-c2664970643]   i[_ngcontent-ng-c2664970643] {
  color: var(--primary-color);
}
.card-body[_ngcontent-ng-c2664970643] {
  padding: 25px;
}
.alert[_ngcontent-ng-c2664970643] {
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}
.alert[_ngcontent-ng-c2664970643]   i[_ngcontent-ng-c2664970643] {
  margin-top: 3px;
}
.alert-warning[_ngcontent-ng-c2664970643] {
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 4px solid #ffc107;
  color: var(--text-color);
}
.alert-info[_ngcontent-ng-c2664970643] {
  background-color: rgba(13, 202, 240, 0.1);
  border-left: 4px solid #0dcaf0;
  color: var(--text-color);
}
.modern-form[_ngcontent-ng-c2664970643] {
  margin-top: 20px;
}
.form-row[_ngcontent-ng-c2664970643] {
  margin-bottom: 20px;
}
.form-group[_ngcontent-ng-c2664970643] {
  margin-bottom: 20px;
}
.modern-form-label[_ngcontent-ng-c2664970643] {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
}
.input-group[_ngcontent-ng-c2664970643] {
  position: relative;
  display: flex;
  align-items: stretch;
  width: 100%;
}
.input-group-text[_ngcontent-ng-c2664970643] {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  border-right: none;
  color: var(--text-muted);
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  padding: 0.5rem 1rem;
}
.modern-form-control[_ngcontent-ng-c2664970643] {
  flex: 1;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border: 1px solid var(--input-border);
  background-color: var(--input-bg);
  color: var(--input-text);
  border-radius: 0;
  transition: border-color 0.3s, box-shadow 0.3s;
}
.modern-form-control[_ngcontent-ng-c2664970643]:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
  outline: none;
}
.modern-form-control.is-invalid[_ngcontent-ng-c2664970643] {
  border-color: #dc3545;
}
.password-toggle[_ngcontent-ng-c2664970643] {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border: 1px solid var(--input-border);
  background-color: var(--input-bg);
  color: var(--text-muted);
  padding: 0.5rem 1rem;
  cursor: pointer;
}
.password-toggle[_ngcontent-ng-c2664970643]:hover {
  color: var(--primary-color);
}
.invalid-feedback[_ngcontent-ng-c2664970643] {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}
.password-requirements[_ngcontent-ng-c2664970643] {
  background-color: var(--card-background-secondary, #2d2d2d);
  border: none;
  border-radius: 8px;
}
.password-requirements[_ngcontent-ng-c2664970643]   .card-title[_ngcontent-ng-c2664970643] {
  font-size: 1rem;
  margin-bottom: 10px;
}
.requirements-list[_ngcontent-ng-c2664970643] {
  list-style: none;
  padding: 0;
  margin: 0;
}
.requirements-list[_ngcontent-ng-c2664970643]   li[_ngcontent-ng-c2664970643] {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
  color: var(--text-secondary, #a0a0a0);
}
.requirements-list[_ngcontent-ng-c2664970643]   li.fulfilled[_ngcontent-ng-c2664970643] {
  color: var(--text-color, #e0e0e0);
}
.btn-primary[_ngcontent-ng-c2664970643] {
  background-color: var(--primary-color, #4361ee);
  border-color: var(--primary-color, #4361ee);
  color: white;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}
.btn-primary[_ngcontent-ng-c2664970643]:hover:not(:disabled) {
  background-color: var(--secondary-color, #3f37c9);
  border-color: var(--secondary-color, #3f37c9);
  transform: translateY(-2px);
}
.btn-primary[_ngcontent-ng-c2664970643]:disabled {
  background-color: var(--text-muted, #6c757d);
  border-color: var(--text-muted, #6c757d);
  cursor: not-allowed;
  opacity: 0.7;
}
.support[_ngcontent-ng-c2664970643] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--text-muted, #6c757d);
  font-size: 0.9rem;
}
.support[_ngcontent-ng-c2664970643]   i[_ngcontent-ng-c2664970643] {
  color: var(--primary-color, #4361ee);
}
.support[_ngcontent-ng-c2664970643]   a[_ngcontent-ng-c2664970643] {
  color: var(--primary-color, #4361ee);
  text-decoration: none;
  transition: color 0.3s;
}
.support[_ngcontent-ng-c2664970643]   a[_ngcontent-ng-c2664970643]:hover {
  text-decoration: underline;
}
@media (max-width: 768px) {
  .card-body[_ngcontent-ng-c2664970643] {
    padding: 20px 15px;
  }
  .card-title[_ngcontent-ng-c2664970643] {
    font-size: 1.3rem;
  }
  .btn-lg[_ngcontent-ng-c2664970643] {
    padding: 8px 16px;
    font-size: 0.95rem;
  }
}
/*# sourceMappingURL=/change-password.component.css.map */</style><style ng-app-id="ng">

.sidebar[_ngcontent-ng-c101594867] {
  width: 280px;
  height: 100%;
  background-color: var(--sidebar-bg);
  color: var(--text-primary);
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 20px var(--shadow-color);
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  z-index: 1000;
}
.sidebar.collapsed[_ngcontent-ng-c101594867] {
  width: 80px;
}
.sidebar-header[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  height: 80px;
}
.logo-container[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  gap: 12px;
}
.logo-container[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  font-size: 24px;
  color: var(--primary-color);
  transition: transform 0.3s ease;
}
.logo-container[_ngcontent-ng-c101594867]:hover   i[_ngcontent-ng-c101594867] {
  transform: scale(1.1);
}
.logo-text[_ngcontent-ng-c101594867] {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: 0.5px;
  transition: opacity 0.3s ease;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .logo-text[_ngcontent-ng-c101594867] {
  opacity: 0;
}
.toggle-btn[_ngcontent-ng-c101594867] {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}
.toggle-btn[_ngcontent-ng-c101594867]:hover {
  background-color: var(--sidebar-hover);
  color: var(--primary-color);
  transform: scale(1.1);
}
.sidebar-content[_ngcontent-ng-c101594867] {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}
.menu-section[_ngcontent-ng-c101594867] {
  margin-bottom: 20px;
}
.menu-header[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 10px;
  margin: 0 10px;
  position: relative;
}
.menu-header[_ngcontent-ng-c101594867]:hover {
  background-color: var(--sidebar-hover);
  transform: translateX(5px);
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-header[_ngcontent-ng-c101594867] {
  justify-content: center;
  padding: 12px;
  margin: 0 5px;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-header[_ngcontent-ng-c101594867]:hover {
  transform: scale(1.05);
}
.menu-icon[_ngcontent-ng-c101594867] {
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.1) 0%,
      rgba(63, 55, 201, 0.1) 100%);
  margin-right: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}
.menu-header[_ngcontent-ng-c101594867]:hover   .menu-icon[_ngcontent-ng-c101594867] {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-icon[_ngcontent-ng-c101594867] {
  margin-right: 0;
  width: 38px;
  height: 38px;
}
.menu-icon[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  font-size: 18px;
  color: var(--primary-color);
  transition: transform 0.3s ease;
}
.menu-header[_ngcontent-ng-c101594867]:hover   .menu-icon[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  transform: scale(1.1);
}
.menu-title[_ngcontent-ng-c101594867] {
  flex: 1;
  font-weight: 600;
  font-size: 15px;
  letter-spacing: 0.3px;
}
.menu-arrow[_ngcontent-ng-c101594867] {
  transition: transform 0.3s ease;
  color: var(--text-muted);
  font-size: 12px;
}
.menu-arrow.rotated[_ngcontent-ng-c101594867] {
  transform: rotate(180deg);
}
.menu-items[_ngcontent-ng-c101594867] {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
}
.menu-items.expanded[_ngcontent-ng-c101594867] {
  max-height: 2000px;
  transition: max-height 1s ease-in-out;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-items[_ngcontent-ng-c101594867] {
  max-height: 2000px;
}
.menu-item[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  padding: 10px 20px 10px 10px;
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.3s ease;
  white-space: nowrap;
  border-radius: 8px;
  margin: 4px 8px;
  position: relative;
}
.menu-item[_ngcontent-ng-c101594867]::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background:
    linear-gradient(
      90deg,
      var(--primary-color),
      transparent);
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 8px;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867] {
  padding: 10px;
  justify-content: center;
  margin: 4px;
}
.menu-item[_ngcontent-ng-c101594867]:hover {
  background-color: var(--sidebar-hover);
  color: var(--text-color);
  transform: translateX(5px);
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867]:hover {
  transform: scale(1.05);
}
.menu-item.active[_ngcontent-ng-c101594867] {
  background-color: var(--sidebar-active);
  color: var(--primary-color);
  font-weight: 600;
}
.menu-item.active[_ngcontent-ng-c101594867]::before {
  width: 4px;
  opacity: 1;
}
.menu-item[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  font-size: 16px;
  width: 20px;
  text-align: center;
  margin-right: 12px;
  position: relative;
  z-index: 1;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  margin-right: 0;
  font-size: 18px;
}
.menu-item.single-item[_ngcontent-ng-c101594867] {
  margin: 8px;
  background-color: var(--sidebar-hover);
  border: 1px solid var(--border-color);
}
.menu-item.single-item[_ngcontent-ng-c101594867]:hover {
  background-color: var(--sidebar-active);
  border-color: var(--primary-color);
}
.sidebar-footer[_ngcontent-ng-c101594867] {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.user-profile-container[_ngcontent-ng-c101594867] {
  margin-bottom: 10px;
}
.profile-btn[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 15px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--text-color);
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.1) 0%,
      rgba(63, 55, 201, 0.1) 100%);
  font-weight: 500;
  width: 100%;
}
.profile-btn[_ngcontent-ng-c101594867]:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.2) 0%,
      rgba(63, 55, 201, 0.2) 100%);
}
.profile-image-container[_ngcontent-ng-c101594867] {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.profile-image[_ngcontent-ng-c101594867] {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
  transition: all 0.3s ease;
}
.profile-image[_ngcontent-ng-c101594867]:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(67, 97, 238, 0.3);
}
.profile-icon[_ngcontent-ng-c101594867] {
  font-size: 32px;
  color: var(--primary-color);
  transition: all 0.3s ease;
}
.profile-icon[_ngcontent-ng-c101594867]:hover {
  transform: scale(1.1);
  color: var(--primary-color);
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .profile-btn[_ngcontent-ng-c101594867] {
  justify-content: center;
  padding: 12px;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .profile-image-container[_ngcontent-ng-c101594867] {
  width: 36px;
  height: 36px;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .profile-image[_ngcontent-ng-c101594867] {
  width: 36px;
  height: 36px;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .profile-icon[_ngcontent-ng-c101594867] {
  font-size: 36px;
}
.theme-toggle-container[_ngcontent-ng-c101594867] {
  margin-bottom: 10px;
}
.theme-toggle-btn[_ngcontent-ng-c101594867], 
.logout-btn[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 15px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--text-color);
  background: transparent;
  border: none;
  font-weight: 500;
  width: 100%;
}
.theme-toggle-btn[_ngcontent-ng-c101594867] {
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.1) 0%,
      rgba(63, 55, 201, 0.1) 100%);
}
.logout-btn[_ngcontent-ng-c101594867] {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}
.theme-toggle-btn[_ngcontent-ng-c101594867]:hover, 
.logout-btn[_ngcontent-ng-c101594867]:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.theme-toggle-btn[_ngcontent-ng-c101594867]:hover {
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.2) 0%,
      rgba(63, 55, 201, 0.2) 100%);
}
.logout-btn[_ngcontent-ng-c101594867]:hover {
  background-color: rgba(220, 53, 69, 0.2);
}
.theme-toggle-btn.icon-only[_ngcontent-ng-c101594867], 
.logout-btn.icon-only[_ngcontent-ng-c101594867] {
  justify-content: center;
  padding: 12px;
}
.theme-toggle-btn[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  font-size: 18px;
  color: var(--primary-color);
}
.logout-btn[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  font-size: 18px;
  color: #dc3545;
}
@media (max-width: 991.98px) {
  .sidebar[_ngcontent-ng-c101594867] {
    position: fixed;
    z-index: 1000;
    width: 260px;
  }
  .sidebar.collapsed[_ngcontent-ng-c101594867] {
    transform: translateX(-100%);
  }
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-header[_ngcontent-ng-c101594867], 
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867] {
  position: relative;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-header[_ngcontent-ng-c101594867]::after, 
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867]::after {
  content: attr(data-title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--card-bg-color);
  color: var(--text-color);
  padding: 5px 10px;
  border-radius: 5px;
  white-space: nowrap;
  box-shadow: 0 2px 5px var(--shadow-color);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 1001;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-header[_ngcontent-ng-c101594867]:hover::after, 
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867]:hover::after {
  opacity: 1;
  transform: translateY(-50%) translateX(10px);
}
.logo-link[_ngcontent-ng-c101594867] {
  text-decoration: none;
  color: inherit;
  display: inline-block;
  cursor: pointer;
}
.logo-link[_ngcontent-ng-c101594867]:hover, 
.logo-link[_ngcontent-ng-c101594867]:focus {
  text-decoration: none;
  color: inherit;
}
/*# sourceMappingURL=/sidebar.component.css.map */</style></head>
<body class="mat-typography">
  <app-root ng-version="19.2.5" _nghost-ng-c4172328733="" ng-server-context="ssg"><div _ngcontent-ng-c4172328733="" class="app-initializing"><div _ngcontent-ng-c4172328733="" class="initializing-spinner"><div _ngcontent-ng-c4172328733="" class="spinner-circle"></div><div _ngcontent-ng-c4172328733="" class="dumbbell"><div _ngcontent-ng-c4172328733="" class="weight left"><div _ngcontent-ng-c4172328733="" class="inner-weight"></div></div><div _ngcontent-ng-c4172328733="" class="handle"></div><div _ngcontent-ng-c4172328733="" class="weight right"><div _ngcontent-ng-c4172328733="" class="inner-weight"></div></div></div></div></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--><div _ngcontent-ng-c4172328733="" class="app-container initializing" ng-reflect-ng-class="[object Object]"><app-sidebar _ngcontent-ng-c4172328733="" _nghost-ng-c101594867="" ng-reflect-collapsed="false" ng-reflect-is-dark-mode="true"><div _ngcontent-ng-c101594867="" class="sidebar" ng-reflect-ng-class="[object Object]"><div _ngcontent-ng-c101594867="" class="sidebar-header"><a _ngcontent-ng-c101594867="" routerlink="/todayentries" class="logo-link" ng-reflect-router-link="/todayentries" href="/todayentries"><div _ngcontent-ng-c101594867="" class="logo-container"><i _ngcontent-ng-c101594867="" class="fas fa-dumbbell"></i><span _ngcontent-ng-c101594867="" class="logo-text">GymKod Pro</span><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div></a><button _ngcontent-ng-c101594867="" title="Kenar Çubuğunu Daralt/Genişlet" class="toggle-btn"><i _ngcontent-ng-c101594867="" class="fas fa-angle-left" ng-reflect-ng-class="fa-angle-left"></i></button></div><div _ngcontent-ng-c101594867="" class="sidebar-content"><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><div _ngcontent-ng-c101594867="" class="sidebar-footer"><!--bindings={
  "ng-reflect-ng-if": "false"
}--><div _ngcontent-ng-c101594867="" class="theme-toggle-container"><button _ngcontent-ng-c101594867="" class="theme-toggle-btn"><i _ngcontent-ng-c101594867="" class="fas fa-sun" ng-reflect-ng-class="fa-sun"></i><span _ngcontent-ng-c101594867="">Aydınlık Mod</span></button><!--bindings={
  "ng-reflect-ng-if": "true"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div></div></app-sidebar><!--bindings={
  "ng-reflect-ng-if": "true"
}--><div _ngcontent-ng-c4172328733="" class="main-content"><div _ngcontent-ng-c4172328733="" class="mobile-header"><button _ngcontent-ng-c4172328733="" class="sidebar-toggle"><i _ngcontent-ng-c4172328733="" class="fas fa-times" ng-reflect-ng-class="fa-times"></i></button><div _ngcontent-ng-c4172328733="" class="mobile-title">Spor Salonu</div><button _ngcontent-ng-c4172328733="" class="theme-toggle"><i _ngcontent-ng-c4172328733="" class="fas fa-sun" ng-reflect-ng-class="fa-sun"></i></button></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--><main _ngcontent-ng-c4172328733="" class="content-area"><router-outlet _ngcontent-ng-c4172328733=""></router-outlet><app-change-password _nghost-ng-c2664970643=""><div _ngcontent-ng-c2664970643="" class="main-content"><div _ngcontent-ng-c2664970643="" class="container-fluid"><div _ngcontent-ng-c2664970643="" class="row"><div _ngcontent-ng-c2664970643="" class="col-md-8 offset-md-2"><div _ngcontent-ng-c2664970643="" class="card"><div _ngcontent-ng-c2664970643="" class="card-header"><h2 _ngcontent-ng-c2664970643="" class="card-title"><i _ngcontent-ng-c2664970643="" class="fas fa-lock"></i></h2></div><div _ngcontent-ng-c2664970643="" class="card-body"><!--container--><!--container--><form _ngcontent-ng-c2664970643="" novalidate="" class="modern-form"><div _ngcontent-ng-c2664970643="" class="form-row"><div _ngcontent-ng-c2664970643="" class="form-group col-md-12"><label _ngcontent-ng-c2664970643="" for="currentPassword" class="modern-form-label">Mevcut Şifre</label><div _ngcontent-ng-c2664970643="" class="input-group"><span _ngcontent-ng-c2664970643="" class="input-group-text"><i _ngcontent-ng-c2664970643="" class="fas fa-key"></i></span><input _ngcontent-ng-c2664970643="" id="currentPassword" formcontrolname="currentPassword" placeholder="Mevcut şifreniz" class="modern-form-control" ng-reflect-name="currentPassword"><button _ngcontent-ng-c2664970643="" type="button" class="btn btn-outline-secondary password-toggle"><i _ngcontent-ng-c2664970643=""></i></button></div><!--container--></div></div><div _ngcontent-ng-c2664970643="" class="form-row"><div _ngcontent-ng-c2664970643="" class="form-group col-md-12"><label _ngcontent-ng-c2664970643="" for="newPassword" class="modern-form-label">Yeni Şifre</label><div _ngcontent-ng-c2664970643="" class="input-group"><span _ngcontent-ng-c2664970643="" class="input-group-text"><i _ngcontent-ng-c2664970643="" class="fas fa-lock"></i></span><input _ngcontent-ng-c2664970643="" id="newPassword" formcontrolname="newPassword" placeholder="Yeni şifreniz (en az 6 karakter)" class="modern-form-control" ng-reflect-name="newPassword"><button _ngcontent-ng-c2664970643="" type="button" class="btn btn-outline-secondary password-toggle"><i _ngcontent-ng-c2664970643=""></i></button></div><!--container--></div></div><div _ngcontent-ng-c2664970643="" class="form-row"><div _ngcontent-ng-c2664970643="" class="form-group col-md-12"><label _ngcontent-ng-c2664970643="" for="confirmPassword" class="modern-form-label">Şifre Tekrarı</label><div _ngcontent-ng-c2664970643="" class="input-group"><span _ngcontent-ng-c2664970643="" class="input-group-text"><i _ngcontent-ng-c2664970643="" class="fas fa-lock"></i></span><input _ngcontent-ng-c2664970643="" id="confirmPassword" formcontrolname="confirmPassword" placeholder="Yeni şifrenizi tekrar girin" class="modern-form-control" ng-reflect-name="confirmPassword"><button _ngcontent-ng-c2664970643="" type="button" class="btn btn-outline-secondary password-toggle"><i _ngcontent-ng-c2664970643=""></i></button></div><!--container--></div></div><div _ngcontent-ng-c2664970643="" class="password-requirements card mt-3 mb-4"><div _ngcontent-ng-c2664970643="" class="card-body"><h5 _ngcontent-ng-c2664970643="" class="card-title">Şifre Gereksinimleri:</h5><ul _ngcontent-ng-c2664970643="" class="requirements-list"><li _ngcontent-ng-c2664970643=""><i _ngcontent-ng-c2664970643=""></i> En az 6 karakter </li></ul></div></div><div _ngcontent-ng-c2664970643="" class="form-group text-center"><button _ngcontent-ng-c2664970643="" type="submit" class="btn btn-primary btn-lg"><!--container--><!--container--></button></div></form><div _ngcontent-ng-c2664970643="" class="text-center mt-4"><div _ngcontent-ng-c2664970643="" class="support"><i _ngcontent-ng-c2664970643="" class="fas fa-headset"></i><span _ngcontent-ng-c2664970643="">Destek: <a _ngcontent-ng-c2664970643="" href="mailto:<EMAIL>"><EMAIL></a></span></div></div></div></div></div></div></div></div></app-change-password><!--container--></main></div></div></app-root>
<script src="polyfills.js" type="module"></script><script src="scripts.js" defer=""></script><script src="main.js" type="module"></script>

</body></html>