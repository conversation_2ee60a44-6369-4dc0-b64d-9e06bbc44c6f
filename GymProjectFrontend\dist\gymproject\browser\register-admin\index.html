<!DOCTYPE html><html lang="en"><head>
  <meta charset="utf-8">
  <title>Spor Salonu QR</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&amp;display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="styles.css"><link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&amp;display=swap" as="style"><link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style"><style ng-app-id="ng">

.app-initializing[_ngcontent-ng-c4172328733] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-color);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
.initializing-spinner[_ngcontent-ng-c4172328733] {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.spinner-circle[_ngcontent-ng-c4172328733] {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid transparent;
  border-top-color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  animation: _ngcontent-ng-c4172328733_spin 1.5s linear infinite;
}
.dumbbell[_ngcontent-ng-c4172328733] {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: _ngcontent-ng-c4172328733_lift 2s ease-in-out infinite;
}
.weight[_ngcontent-ng-c4172328733] {
  background:
    linear-gradient(
      135deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(67, 97, 238, 0.5);
}
.inner-weight[_ngcontent-ng-c4172328733] {
  width: 50%;
  height: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}
.weight.left[_ngcontent-ng-c4172328733] {
  animation: _ngcontent-ng-c4172328733_pulse-left 2s ease-in-out infinite;
}
.weight.right[_ngcontent-ng-c4172328733] {
  animation: _ngcontent-ng-c4172328733_pulse-right 2s ease-in-out infinite;
}
.handle[_ngcontent-ng-c4172328733] {
  height: 8px;
  width: 50px;
  background:
    linear-gradient(
      90deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(67, 97, 238, 0.5);
}
@keyframes _ngcontent-ng-c4172328733_spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes _ngcontent-ng-c4172328733_lift {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}
@keyframes _ngcontent-ng-c4172328733_pulse-left {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
@keyframes _ngcontent-ng-c4172328733_pulse-right {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
.app-container[_ngcontent-ng-c4172328733] {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  opacity: 1;
  visibility: visible;
}
.app-container.initializing[_ngcontent-ng-c4172328733] {
  opacity: 0;
  visibility: hidden;
}
.main-content[_ngcontent-ng-c4172328733] {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}
.content-area[_ngcontent-ng-c4172328733] {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  transition: all 0.3s ease;
}
.mobile-header[_ngcontent-ng-c4172328733] {
  display: none;
  padding: 15px;
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px var(--shadow-color);
  z-index: 100;
}
.mobile-title[_ngcontent-ng-c4172328733] {
  font-size: 18px;
  font-weight: 600;
}
.sidebar-toggle[_ngcontent-ng-c4172328733], 
.theme-toggle[_ngcontent-ng-c4172328733] {
  background: none;
  border: none;
  color: var(--sidebar-text);
  font-size: 18px;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.sidebar-toggle[_ngcontent-ng-c4172328733]:hover, 
.theme-toggle[_ngcontent-ng-c4172328733]:hover {
  background-color: var(--sidebar-hover);
}
@media (max-width: 991.98px) {
  .mobile-header[_ngcontent-ng-c4172328733] {
    display: flex;
  }
  .app-container.sidebar-collapsed[_ngcontent-ng-c4172328733]   .main-content[_ngcontent-ng-c4172328733] {
    margin-left: 0;
  }
}
body.initializing[_ngcontent-ng-c4172328733] {
  overflow: hidden;
}
/*# sourceMappingURL=/app.component.css.map */</style><style ng-app-id="ng">

.register-container[_ngcontent-ng-c1832293973] {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--background-color);
  background-image:
    linear-gradient(
      135deg,
      #f5f7fa 0%,
      #e4e8f0 100%);
}
.register-wrapper[_ngcontent-ng-c1832293973] {
  display: flex;
  width: 90%;
  max-width: 1200px;
  height: 700px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 20px 40px var(--shadow-color);
  position: relative;
}
.register-image-panel[_ngcontent-ng-c1832293973] {
  flex: 1.2;
  background-image: url(https://images.unsplash.com/photo-1571902943202-507ec2618e8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80);
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.overlay[_ngcontent-ng-c1832293973] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.85) 0%,
      rgba(58, 12, 163, 0.85) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}
.gym-branding[_ngcontent-ng-c1832293973] {
  text-align: center;
  color: white;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  max-width: 500px;
}
.logo-container[_ngcontent-ng-c1832293973] {
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
}
.gym-branding[_ngcontent-ng-c1832293973]   i[_ngcontent-ng-c1832293973] {
  font-size: 50px;
  color: white;
}
.gym-branding[_ngcontent-ng-c1832293973]   h1[_ngcontent-ng-c1832293973] {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.gym-branding[_ngcontent-ng-c1832293973]   p[_ngcontent-ng-c1832293973] {
  font-size: 18px;
  opacity: 0.9;
  margin-bottom: 30px;
}
.features[_ngcontent-ng-c1832293973] {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  max-width: 300px;
}
.feature[_ngcontent-ng-c1832293973] {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
  border-radius: 10px;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}
.feature[_ngcontent-ng-c1832293973]:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}
.feature[_ngcontent-ng-c1832293973]   i[_ngcontent-ng-c1832293973] {
  font-size: 20px;
  color: white;
}
.feature[_ngcontent-ng-c1832293973]   span[_ngcontent-ng-c1832293973] {
  font-size: 16px;
  font-weight: 500;
}
.register-form-panel[_ngcontent-ng-c1832293973] {
  flex: 0.8;
  background-color: var(--card-bg-color);
  padding: 20px 40px 40px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
}
.register-form-panel[_ngcontent-ng-c1832293973]::before {
  content: "";
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: var(--primary-color);
  opacity: 0.1;
  border-radius: 50%;
}
.register-form-panel[_ngcontent-ng-c1832293973]::after {
  content: "";
  position: absolute;
  bottom: -80px;
  left: -80px;
  width: 160px;
  height: 160px;
  background: var(--secondary-color);
  opacity: 0.1;
  border-radius: 50%;
}
.register-form-container[_ngcontent-ng-c1832293973] {
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  z-index: 1;
}
.register-header[_ngcontent-ng-c1832293973] {
  margin-top: 0;
  margin-bottom: 15px;
  text-align: center;
}
.register-header[_ngcontent-ng-c1832293973]   h2[_ngcontent-ng-c1832293973] {
  color: var(--text-color);
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}
.register-header[_ngcontent-ng-c1832293973]   p[_ngcontent-ng-c1832293973] {
  color: var(--text-muted);
  font-size: 14px;
  margin-bottom: 0;
}
.ban-warning[_ngcontent-ng-c1832293973] {
  background:
    linear-gradient(
      135deg,
      #ff6b6b 0%,
      #ee5a52 100%);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}
.ban-warning[_ngcontent-ng-c1832293973]   i[_ngcontent-ng-c1832293973] {
  font-size: 16px;
}
.register-form[_ngcontent-ng-c1832293973] {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.form-row[_ngcontent-ng-c1832293973] {
  display: flex;
  gap: 10px;
}
.form-row[_ngcontent-ng-c1832293973]   .form-group[_ngcontent-ng-c1832293973] {
  flex: 1;
}
.form-group[_ngcontent-ng-c1832293973] {
  position: relative;
}
.form-group[_ngcontent-ng-c1832293973]   label[_ngcontent-ng-c1832293973] {
  display: block;
  margin-bottom: 5px;
  color: var(--text-color);
  font-weight: 500;
  font-size: 13px;
}
.input-group[_ngcontent-ng-c1832293973] {
  position: relative;
  display: flex;
  align-items: center;
}
.input-group[_ngcontent-ng-c1832293973]   i[_ngcontent-ng-c1832293973] {
  position: absolute;
  left: 15px;
  color: var(--text-muted);
  font-size: 16px;
}
.form-group[_ngcontent-ng-c1832293973]   input[_ngcontent-ng-c1832293973] {
  width: 100%;
  padding: 10px 15px 10px 45px;
  border: 1px solid var(--input-border);
  border-radius: 10px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--input-text);
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
.form-group[_ngcontent-ng-c1832293973]   input[_ngcontent-ng-c1832293973]:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
  outline: none;
}
.form-group[_ngcontent-ng-c1832293973]   input.is-invalid[_ngcontent-ng-c1832293973] {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(249, 65, 68, 0.2);
}
.password-toggle[_ngcontent-ng-c1832293973] {
  position: absolute;
  right: 15px;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.password-toggle[_ngcontent-ng-c1832293973]:hover {
  color: var(--primary-color);
}
.error-message[_ngcontent-ng-c1832293973] {
  color: var(--danger-color);
  font-size: 12px;
  margin-top: 6px;
  font-weight: 500;
}
.form-actions[_ngcontent-ng-c1832293973] {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
}
.login-link[_ngcontent-ng-c1832293973] {
  color: var(--primary-color);
  font-size: 14px;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}
.login-link[_ngcontent-ng-c1832293973]:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}
.register-button[_ngcontent-ng-c1832293973] {
  background:
    linear-gradient(
      135deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%);
  color: white;
  padding: 10px;
  border: none;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 42px;
  box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
  letter-spacing: 0.5px;
  width: 100%;
  margin-top: 10px;
}
.register-button[_ngcontent-ng-c1832293973]:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
}
.register-button[_ngcontent-ng-c1832293973]:active {
  transform: translateY(1px);
}
.register-button[_ngcontent-ng-c1832293973]:disabled {
  background:
    linear-gradient(
      135deg,
      #a0a0a0 0%,
      #7a7a7a 100%);
  cursor: not-allowed;
  box-shadow: none;
}
.register-footer[_ngcontent-ng-c1832293973] {
  margin-top: 15px;
  text-align: center;
}
.info-text[_ngcontent-ng-c1832293973] {
  color: var(--text-muted);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  background: rgba(67, 97, 238, 0.1);
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
}
.info-text[_ngcontent-ng-c1832293973]   i[_ngcontent-ng-c1832293973] {
  color: var(--primary-color);
  font-size: 14px;
}
@media (max-width: 1199.98px) {
  .register-wrapper[_ngcontent-ng-c1832293973] {
    max-width: 1000px;
  }
}
@media (max-width: 991.98px) {
  .register-wrapper[_ngcontent-ng-c1832293973] {
    flex-direction: column;
    height: auto;
    max-width: 600px;
  }
  .register-image-panel[_ngcontent-ng-c1832293973] {
    height: 300px;
  }
  .register-form-panel[_ngcontent-ng-c1832293973] {
    padding: 40px 30px 60px;
  }
  .features[_ngcontent-ng-c1832293973] {
    flex-direction: row;
    max-width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
  .feature[_ngcontent-ng-c1832293973] {
    width: calc(50% - 10px);
  }
}
@media (max-width: 767.98px) {
  .register-wrapper[_ngcontent-ng-c1832293973] {
    width: 95%;
  }
  .register-image-panel[_ngcontent-ng-c1832293973] {
    height: 250px;
  }
  .gym-branding[_ngcontent-ng-c1832293973]   h1[_ngcontent-ng-c1832293973] {
    font-size: 32px;
  }
  .gym-branding[_ngcontent-ng-c1832293973]   p[_ngcontent-ng-c1832293973] {
    font-size: 16px;
    margin-bottom: 20px;
  }
  .features[_ngcontent-ng-c1832293973] {
    gap: 10px;
  }
  .feature[_ngcontent-ng-c1832293973] {
    padding: 10px 15px;
  }
  .feature[_ngcontent-ng-c1832293973]   i[_ngcontent-ng-c1832293973] {
    font-size: 18px;
  }
  .feature[_ngcontent-ng-c1832293973]   span[_ngcontent-ng-c1832293973] {
    font-size: 14px;
  }
}
@media (max-width: 575.98px) {
  .register-wrapper[_ngcontent-ng-c1832293973] {
    border-radius: 10px;
  }
  .register-image-panel[_ngcontent-ng-c1832293973] {
    height: 200px;
  }
  .logo-container[_ngcontent-ng-c1832293973] {
    width: 80px;
    height: 80px;
  }
  .gym-branding[_ngcontent-ng-c1832293973]   i[_ngcontent-ng-c1832293973] {
    font-size: 40px;
  }
  .gym-branding[_ngcontent-ng-c1832293973]   h1[_ngcontent-ng-c1832293973] {
    font-size: 28px;
  }
  .gym-branding[_ngcontent-ng-c1832293973]   p[_ngcontent-ng-c1832293973] {
    font-size: 14px;
    margin-bottom: 15px;
  }
  .features[_ngcontent-ng-c1832293973] {
    flex-direction: column;
  }
  .feature[_ngcontent-ng-c1832293973] {
    width: 100%;
  }
  .register-form-panel[_ngcontent-ng-c1832293973] {
    padding: 30px 20px 50px;
  }
  .register-header[_ngcontent-ng-c1832293973]   h2[_ngcontent-ng-c1832293973] {
    font-size: 24px;
  }
  .register-header[_ngcontent-ng-c1832293973]   p[_ngcontent-ng-c1832293973] {
    font-size: 14px;
  }
  .form-row[_ngcontent-ng-c1832293973] {
    flex-direction: column;
    gap: 15px;
  }
  .register-button[_ngcontent-ng-c1832293973] {
    height: 40px;
    font-size: 14px;
    margin-top: 5px;
  }
}
[data-theme="dark"][_nghost-ng-c1832293973]   .input-group[_ngcontent-ng-c1832293973]   input[_ngcontent-ng-c1832293973], [data-theme="dark"]   [_nghost-ng-c1832293973]   .input-group[_ngcontent-ng-c1832293973]   input[_ngcontent-ng-c1832293973], 
[data-theme="dark"][_nghost-ng-c1832293973]   .input-group[_ngcontent-ng-c1832293973]   select[_ngcontent-ng-c1832293973], [data-theme="dark"]   [_nghost-ng-c1832293973]   .input-group[_ngcontent-ng-c1832293973]   select[_ngcontent-ng-c1832293973] {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-text);
}
[data-theme="dark"][_nghost-ng-c1832293973]   .register-form-panel[_ngcontent-ng-c1832293973], [data-theme="dark"]   [_nghost-ng-c1832293973]   .register-form-panel[_ngcontent-ng-c1832293973] {
  background-color: var(--card-bg-color);
}
[data-theme="dark"][_nghost-ng-c1832293973]   .register-header[_ngcontent-ng-c1832293973]   h2[_ngcontent-ng-c1832293973], [data-theme="dark"]   [_nghost-ng-c1832293973]   .register-header[_ngcontent-ng-c1832293973]   h2[_ngcontent-ng-c1832293973], 
[data-theme="dark"][_nghost-ng-c1832293973]   .form-group[_ngcontent-ng-c1832293973]   label[_ngcontent-ng-c1832293973], [data-theme="dark"]   [_nghost-ng-c1832293973]   .form-group[_ngcontent-ng-c1832293973]   label[_ngcontent-ng-c1832293973] {
  color: var(--text-color);
}
[data-theme="dark"][_nghost-ng-c1832293973]   .register-footer[_ngcontent-ng-c1832293973], [data-theme="dark"]   [_nghost-ng-c1832293973]   .register-footer[_ngcontent-ng-c1832293973], 
[data-theme="dark"][_nghost-ng-c1832293973]   .register-header[_ngcontent-ng-c1832293973]   p[_ngcontent-ng-c1832293973], [data-theme="dark"]   [_nghost-ng-c1832293973]   .register-header[_ngcontent-ng-c1832293973]   p[_ngcontent-ng-c1832293973] {
  color: var(--text-muted);
}
[data-theme="dark"][_nghost-ng-c1832293973]   .input-group[_ngcontent-ng-c1832293973]   i[_ngcontent-ng-c1832293973], [data-theme="dark"]   [_nghost-ng-c1832293973]   .input-group[_ngcontent-ng-c1832293973]   i[_ngcontent-ng-c1832293973], 
[data-theme="dark"][_nghost-ng-c1832293973]   .input-group[_ngcontent-ng-c1832293973]   .password-toggle[_ngcontent-ng-c1832293973], [data-theme="dark"]   [_nghost-ng-c1832293973]   .input-group[_ngcontent-ng-c1832293973]   .password-toggle[_ngcontent-ng-c1832293973] {
  color: var(--text-muted);
}
[data-theme="dark"][_nghost-ng-c1832293973]   .register-container[_ngcontent-ng-c1832293973], [data-theme="dark"]   [_nghost-ng-c1832293973]   .register-container[_ngcontent-ng-c1832293973] {
  background-image:
    linear-gradient(
      135deg,
      #121212 0%,
      #1a1a1a 100%);
}
/*# sourceMappingURL=/register-admin.component.css.map */</style><style ng-app-id="ng">

.sidebar[_ngcontent-ng-c101594867] {
  width: 280px;
  height: 100%;
  background-color: var(--sidebar-bg);
  color: var(--text-primary);
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 20px var(--shadow-color);
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  z-index: 1000;
}
.sidebar.collapsed[_ngcontent-ng-c101594867] {
  width: 80px;
}
.sidebar-header[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  height: 80px;
}
.logo-container[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  gap: 12px;
}
.logo-container[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  font-size: 24px;
  color: var(--primary-color);
  transition: transform 0.3s ease;
}
.logo-container[_ngcontent-ng-c101594867]:hover   i[_ngcontent-ng-c101594867] {
  transform: scale(1.1);
}
.logo-text[_ngcontent-ng-c101594867] {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: 0.5px;
  transition: opacity 0.3s ease;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .logo-text[_ngcontent-ng-c101594867] {
  opacity: 0;
}
.toggle-btn[_ngcontent-ng-c101594867] {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}
.toggle-btn[_ngcontent-ng-c101594867]:hover {
  background-color: var(--sidebar-hover);
  color: var(--primary-color);
  transform: scale(1.1);
}
.sidebar-content[_ngcontent-ng-c101594867] {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}
.menu-section[_ngcontent-ng-c101594867] {
  margin-bottom: 20px;
}
.menu-header[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 10px;
  margin: 0 10px;
  position: relative;
}
.menu-header[_ngcontent-ng-c101594867]:hover {
  background-color: var(--sidebar-hover);
  transform: translateX(5px);
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-header[_ngcontent-ng-c101594867] {
  justify-content: center;
  padding: 12px;
  margin: 0 5px;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-header[_ngcontent-ng-c101594867]:hover {
  transform: scale(1.05);
}
.menu-icon[_ngcontent-ng-c101594867] {
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.1) 0%,
      rgba(63, 55, 201, 0.1) 100%);
  margin-right: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}
.menu-header[_ngcontent-ng-c101594867]:hover   .menu-icon[_ngcontent-ng-c101594867] {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-icon[_ngcontent-ng-c101594867] {
  margin-right: 0;
  width: 38px;
  height: 38px;
}
.menu-icon[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  font-size: 18px;
  color: var(--primary-color);
  transition: transform 0.3s ease;
}
.menu-header[_ngcontent-ng-c101594867]:hover   .menu-icon[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  transform: scale(1.1);
}
.menu-title[_ngcontent-ng-c101594867] {
  flex: 1;
  font-weight: 600;
  font-size: 15px;
  letter-spacing: 0.3px;
}
.menu-arrow[_ngcontent-ng-c101594867] {
  transition: transform 0.3s ease;
  color: var(--text-muted);
  font-size: 12px;
}
.menu-arrow.rotated[_ngcontent-ng-c101594867] {
  transform: rotate(180deg);
}
.menu-items[_ngcontent-ng-c101594867] {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
}
.menu-items.expanded[_ngcontent-ng-c101594867] {
  max-height: 2000px;
  transition: max-height 1s ease-in-out;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-items[_ngcontent-ng-c101594867] {
  max-height: 2000px;
}
.menu-item[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  padding: 10px 20px 10px 10px;
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.3s ease;
  white-space: nowrap;
  border-radius: 8px;
  margin: 4px 8px;
  position: relative;
}
.menu-item[_ngcontent-ng-c101594867]::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background:
    linear-gradient(
      90deg,
      var(--primary-color),
      transparent);
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 8px;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867] {
  padding: 10px;
  justify-content: center;
  margin: 4px;
}
.menu-item[_ngcontent-ng-c101594867]:hover {
  background-color: var(--sidebar-hover);
  color: var(--text-color);
  transform: translateX(5px);
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867]:hover {
  transform: scale(1.05);
}
.menu-item.active[_ngcontent-ng-c101594867] {
  background-color: var(--sidebar-active);
  color: var(--primary-color);
  font-weight: 600;
}
.menu-item.active[_ngcontent-ng-c101594867]::before {
  width: 4px;
  opacity: 1;
}
.menu-item[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  font-size: 16px;
  width: 20px;
  text-align: center;
  margin-right: 12px;
  position: relative;
  z-index: 1;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  margin-right: 0;
  font-size: 18px;
}
.menu-item.single-item[_ngcontent-ng-c101594867] {
  margin: 8px;
  background-color: var(--sidebar-hover);
  border: 1px solid var(--border-color);
}
.menu-item.single-item[_ngcontent-ng-c101594867]:hover {
  background-color: var(--sidebar-active);
  border-color: var(--primary-color);
}
.sidebar-footer[_ngcontent-ng-c101594867] {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.user-profile-container[_ngcontent-ng-c101594867] {
  margin-bottom: 10px;
}
.profile-btn[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 15px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--text-color);
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.1) 0%,
      rgba(63, 55, 201, 0.1) 100%);
  font-weight: 500;
  width: 100%;
}
.profile-btn[_ngcontent-ng-c101594867]:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.2) 0%,
      rgba(63, 55, 201, 0.2) 100%);
}
.profile-image-container[_ngcontent-ng-c101594867] {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.profile-image[_ngcontent-ng-c101594867] {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
  transition: all 0.3s ease;
}
.profile-image[_ngcontent-ng-c101594867]:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(67, 97, 238, 0.3);
}
.profile-icon[_ngcontent-ng-c101594867] {
  font-size: 32px;
  color: var(--primary-color);
  transition: all 0.3s ease;
}
.profile-icon[_ngcontent-ng-c101594867]:hover {
  transform: scale(1.1);
  color: var(--primary-color);
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .profile-btn[_ngcontent-ng-c101594867] {
  justify-content: center;
  padding: 12px;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .profile-image-container[_ngcontent-ng-c101594867] {
  width: 36px;
  height: 36px;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .profile-image[_ngcontent-ng-c101594867] {
  width: 36px;
  height: 36px;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .profile-icon[_ngcontent-ng-c101594867] {
  font-size: 36px;
}
.theme-toggle-container[_ngcontent-ng-c101594867] {
  margin-bottom: 10px;
}
.theme-toggle-btn[_ngcontent-ng-c101594867], 
.logout-btn[_ngcontent-ng-c101594867] {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 15px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--text-color);
  background: transparent;
  border: none;
  font-weight: 500;
  width: 100%;
}
.theme-toggle-btn[_ngcontent-ng-c101594867] {
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.1) 0%,
      rgba(63, 55, 201, 0.1) 100%);
}
.logout-btn[_ngcontent-ng-c101594867] {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}
.theme-toggle-btn[_ngcontent-ng-c101594867]:hover, 
.logout-btn[_ngcontent-ng-c101594867]:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.theme-toggle-btn[_ngcontent-ng-c101594867]:hover {
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.2) 0%,
      rgba(63, 55, 201, 0.2) 100%);
}
.logout-btn[_ngcontent-ng-c101594867]:hover {
  background-color: rgba(220, 53, 69, 0.2);
}
.theme-toggle-btn.icon-only[_ngcontent-ng-c101594867], 
.logout-btn.icon-only[_ngcontent-ng-c101594867] {
  justify-content: center;
  padding: 12px;
}
.theme-toggle-btn[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  font-size: 18px;
  color: var(--primary-color);
}
.logout-btn[_ngcontent-ng-c101594867]   i[_ngcontent-ng-c101594867] {
  font-size: 18px;
  color: #dc3545;
}
@media (max-width: 991.98px) {
  .sidebar[_ngcontent-ng-c101594867] {
    position: fixed;
    z-index: 1000;
    width: 260px;
  }
  .sidebar.collapsed[_ngcontent-ng-c101594867] {
    transform: translateX(-100%);
  }
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-header[_ngcontent-ng-c101594867], 
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867] {
  position: relative;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-header[_ngcontent-ng-c101594867]::after, 
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867]::after {
  content: attr(data-title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--card-bg-color);
  color: var(--text-color);
  padding: 5px 10px;
  border-radius: 5px;
  white-space: nowrap;
  box-shadow: 0 2px 5px var(--shadow-color);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 1001;
}
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-header[_ngcontent-ng-c101594867]:hover::after, 
.sidebar.collapsed[_ngcontent-ng-c101594867]   .menu-item[_ngcontent-ng-c101594867]:hover::after {
  opacity: 1;
  transform: translateY(-50%) translateX(10px);
}
.logo-link[_ngcontent-ng-c101594867] {
  text-decoration: none;
  color: inherit;
  display: inline-block;
  cursor: pointer;
}
.logo-link[_ngcontent-ng-c101594867]:hover, 
.logo-link[_ngcontent-ng-c101594867]:focus {
  text-decoration: none;
  color: inherit;
}
/*# sourceMappingURL=/sidebar.component.css.map */</style></head>
<body class="mat-typography">
  <app-root ng-version="19.2.5" _nghost-ng-c4172328733="" ng-server-context="ssg"><div _ngcontent-ng-c4172328733="" class="app-initializing"><div _ngcontent-ng-c4172328733="" class="initializing-spinner"><div _ngcontent-ng-c4172328733="" class="spinner-circle"></div><div _ngcontent-ng-c4172328733="" class="dumbbell"><div _ngcontent-ng-c4172328733="" class="weight left"><div _ngcontent-ng-c4172328733="" class="inner-weight"></div></div><div _ngcontent-ng-c4172328733="" class="handle"></div><div _ngcontent-ng-c4172328733="" class="weight right"><div _ngcontent-ng-c4172328733="" class="inner-weight"></div></div></div></div></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--><div _ngcontent-ng-c4172328733="" class="app-container initializing" ng-reflect-ng-class="[object Object]"><app-sidebar _ngcontent-ng-c4172328733="" _nghost-ng-c101594867="" ng-reflect-collapsed="false" ng-reflect-is-dark-mode="true"><div _ngcontent-ng-c101594867="" class="sidebar" ng-reflect-ng-class="[object Object]"><div _ngcontent-ng-c101594867="" class="sidebar-header"><a _ngcontent-ng-c101594867="" routerlink="/todayentries" class="logo-link" ng-reflect-router-link="/todayentries" href="/todayentries"><div _ngcontent-ng-c101594867="" class="logo-container"><i _ngcontent-ng-c101594867="" class="fas fa-dumbbell"></i><span _ngcontent-ng-c101594867="" class="logo-text">GymKod Pro</span><!--bindings={
  "ng-reflect-ng-if": "true"
}--></div></a><button _ngcontent-ng-c101594867="" title="Kenar Çubuğunu Daralt/Genişlet" class="toggle-btn"><i _ngcontent-ng-c101594867="" class="fas fa-angle-left" ng-reflect-ng-class="fa-angle-left"></i></button></div><div _ngcontent-ng-c101594867="" class="sidebar-content"><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><div _ngcontent-ng-c101594867="" class="sidebar-footer"><!--bindings={
  "ng-reflect-ng-if": "false"
}--><div _ngcontent-ng-c101594867="" class="theme-toggle-container"><button _ngcontent-ng-c101594867="" class="theme-toggle-btn"><i _ngcontent-ng-c101594867="" class="fas fa-sun" ng-reflect-ng-class="fa-sun"></i><span _ngcontent-ng-c101594867="">Aydınlık Mod</span></button><!--bindings={
  "ng-reflect-ng-if": "true"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div></div></app-sidebar><!--bindings={
  "ng-reflect-ng-if": "true"
}--><div _ngcontent-ng-c4172328733="" class="main-content"><div _ngcontent-ng-c4172328733="" class="mobile-header"><button _ngcontent-ng-c4172328733="" class="sidebar-toggle"><i _ngcontent-ng-c4172328733="" class="fas fa-times" ng-reflect-ng-class="fa-times"></i></button><div _ngcontent-ng-c4172328733="" class="mobile-title">Spor Salonu</div><button _ngcontent-ng-c4172328733="" class="theme-toggle"><i _ngcontent-ng-c4172328733="" class="fas fa-sun" ng-reflect-ng-class="fa-sun"></i></button></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--><main _ngcontent-ng-c4172328733="" class="content-area"><router-outlet _ngcontent-ng-c4172328733=""></router-outlet><app-register-admin _nghost-ng-c1832293973=""><div _ngcontent-ng-c1832293973="" class="register-container"><div _ngcontent-ng-c1832293973="" class="register-wrapper"><div _ngcontent-ng-c1832293973="" class="register-image-panel"><div _ngcontent-ng-c1832293973="" class="overlay"><div _ngcontent-ng-c1832293973="" class="gym-branding"><div _ngcontent-ng-c1832293973="" class="logo-container"><i _ngcontent-ng-c1832293973="" class="fas fa-dumbbell"></i></div><h1 _ngcontent-ng-c1832293973="">GymKod Pro</h1><p _ngcontent-ng-c1832293973="">Admin Kayıt Sistemi</p><div _ngcontent-ng-c1832293973="" class="features"><div _ngcontent-ng-c1832293973="" class="feature"><i _ngcontent-ng-c1832293973="" class="fas fa-shield-alt"></i><span _ngcontent-ng-c1832293973="">Admin Erişimi</span></div><div _ngcontent-ng-c1832293973="" class="feature"><i _ngcontent-ng-c1832293973="" class="fas fa-cogs"></i><span _ngcontent-ng-c1832293973="">Sistem Yönetimi</span></div><div _ngcontent-ng-c1832293973="" class="feature"><i _ngcontent-ng-c1832293973="" class="fas fa-users-cog"></i><span _ngcontent-ng-c1832293973="">Kullanıcı Yönetimi</span></div></div></div></div></div><div _ngcontent-ng-c1832293973="" class="register-form-panel"><div _ngcontent-ng-c1832293973="" class="register-form-container"><div _ngcontent-ng-c1832293973="" class="register-header"><h2 _ngcontent-ng-c1832293973="">Admin Hesabı Oluştur</h2><p _ngcontent-ng-c1832293973="">Sistem yöneticisi olarak kayıt olun</p></div><!--container--><form _ngcontent-ng-c1832293973="" novalidate="" class="register-form"><div _ngcontent-ng-c1832293973="" class="form-row"><div _ngcontent-ng-c1832293973="" class="form-group"><label _ngcontent-ng-c1832293973="" for="firstName">Ad</label><div _ngcontent-ng-c1832293973="" class="input-group"><i _ngcontent-ng-c1832293973="" class="fas fa-user"></i><input _ngcontent-ng-c1832293973="" id="firstName" type="text" formcontrolname="firstName" placeholder="Adınız" ng-reflect-name="firstName"></div><!--container--></div><div _ngcontent-ng-c1832293973="" class="form-group"><label _ngcontent-ng-c1832293973="" for="lastName">Soyad</label><div _ngcontent-ng-c1832293973="" class="input-group"><i _ngcontent-ng-c1832293973="" class="fas fa-user"></i><input _ngcontent-ng-c1832293973="" id="lastName" type="text" formcontrolname="lastName" placeholder="Soyadınız" ng-reflect-name="lastName"></div><!--container--></div></div><div _ngcontent-ng-c1832293973="" class="form-group"><label _ngcontent-ng-c1832293973="" for="email">E-posta</label><div _ngcontent-ng-c1832293973="" class="input-group"><i _ngcontent-ng-c1832293973="" class="fas fa-envelope"></i><input _ngcontent-ng-c1832293973="" id="email" type="email" formcontrolname="email" placeholder="<EMAIL>" ng-reflect-name="email"></div><!--container--></div><div _ngcontent-ng-c1832293973="" class="form-group"><label _ngcontent-ng-c1832293973="" for="password">Şifre</label><div _ngcontent-ng-c1832293973="" class="input-group"><i _ngcontent-ng-c1832293973="" class="fas fa-lock"></i><input _ngcontent-ng-c1832293973="" id="password" formcontrolname="password" placeholder="En az 6 karakter" ng-reflect-name="password"><button _ngcontent-ng-c1832293973="" type="button" class="password-toggle"><i _ngcontent-ng-c1832293973=""></i></button></div><!--container--></div><div _ngcontent-ng-c1832293973="" class="form-group"><label _ngcontent-ng-c1832293973="" for="confirmPassword">Şifre Tekrarı</label><div _ngcontent-ng-c1832293973="" class="input-group"><i _ngcontent-ng-c1832293973="" class="fas fa-lock"></i><input _ngcontent-ng-c1832293973="" id="confirmPassword" formcontrolname="confirmPassword" placeholder="Şifrenizi tekrar girin" ng-reflect-name="confirmPassword"><button _ngcontent-ng-c1832293973="" type="button" class="password-toggle"><i _ngcontent-ng-c1832293973=""></i></button></div><!--container--></div><div _ngcontent-ng-c1832293973="" class="form-actions"><a _ngcontent-ng-c1832293973="" routerlink="/login" class="login-link" ng-reflect-router-link="/login" href="/login">Zaten hesabınız var mı? Giriş yapın</a></div><button _ngcontent-ng-c1832293973="" type="submit" class="register-button"><!--container--><!--container--></button></form><div _ngcontent-ng-c1832293973="" class="register-footer"><p _ngcontent-ng-c1832293973="" class="info-text"><i _ngcontent-ng-c1832293973="" class="fas fa-info-circle"></i> Bu kayıt formu sadece sistem yöneticileri içindir. Kayıt olduktan sonra rol ataması için sistem yöneticisi ile iletişime geçin. </p></div></div></div></div></div></app-register-admin><!--container--></main></div></div></app-root>
<script src="polyfills.js" type="module"></script><script src="scripts.js" defer=""></script><script src="main.js" type="module"></script>

</body></html>