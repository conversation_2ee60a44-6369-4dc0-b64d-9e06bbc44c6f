{"version": 3, "sources": ["src/app/components/license-packages-list/license-packages-list.component.css"], "sourcesContent": ["/* License Packages List Component Styles */\r\n\r\n/* Container */\r\n.modern-table-container {\r\n  background-color: var(--bg-primary);\r\n  border-radius: var(--border-radius-lg);\r\n  overflow: hidden;\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* Loading State */\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 3rem;\r\n  min-height: 200px;\r\n}\r\n\r\n/* Empty State */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 3rem 2rem;\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.empty-state-icon {\r\n  font-size: 4rem;\r\n  color: var(--text-muted);\r\n  margin-bottom: 1rem;\r\n  opacity: 0.6;\r\n}\r\n\r\n.empty-state-title {\r\n  color: var(--text-primary);\r\n  margin-bottom: 0.5rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.empty-state-text {\r\n  color: var(--text-secondary);\r\n  margin-bottom: 0;\r\n  font-size: 0.95rem;\r\n}\r\n\r\n/* Table Wrapper */\r\n.modern-table-wrapper {\r\n  overflow: hidden;\r\n}\r\n\r\n.table-responsive {\r\n  overflow-x: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n/* Modern Table */\r\n.modern-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  background-color: var(--bg-primary);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.modern-table thead {\r\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\r\n}\r\n\r\n.modern-table thead th {\r\n  padding: 1rem 0.75rem;\r\n  text-align: left;\r\n  font-weight: 600;\r\n  color: white;\r\n  font-size: 0.85rem;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n  border: none;\r\n  position: relative;\r\n}\r\n\r\n.modern-table thead th:not(:last-child)::after {\r\n  content: '';\r\n  position: absolute;\r\n  right: 0;\r\n  top: 25%;\r\n  height: 50%;\r\n  width: 1px;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.modern-table tbody tr {\r\n  transition: all 0.2s ease;\r\n  border-bottom: 1px solid var(--border-color);\r\n}\r\n\r\n.modern-table tbody tr:hover {\r\n  background-color: var(--bg-secondary);\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.modern-table tbody tr:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.modern-table tbody td {\r\n  padding: 1rem 0.75rem;\r\n  vertical-align: middle;\r\n  border: none;\r\n  color: var(--text-primary);\r\n}\r\n\r\n/* Package Info */\r\n.package-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.package-title {\r\n  font-weight: 600;\r\n  color: var(--text-primary);\r\n  font-size: 0.95rem;\r\n}\r\n\r\n/* Description */\r\n.description-text {\r\n  color: var(--text-secondary);\r\n  font-size: 0.9rem;\r\n  max-width: 200px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* Role Badge */\r\n.role-badge {\r\n  display: inline-block;\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: var(--border-radius-full);\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.role-admin {\r\n  background-color: rgba(52, 152, 219, 0.1);\r\n  color: #3498db;\r\n  border: 1px solid rgba(52, 152, 219, 0.2);\r\n}\r\n\r\n.role-owner {\r\n  background-color: rgba(231, 76, 60, 0.1);\r\n  color: #e74c3c;\r\n  border: 1px solid rgba(231, 76, 60, 0.2);\r\n}\r\n\r\n.role-member {\r\n  background-color: rgba(46, 204, 113, 0.1);\r\n  color: #2ecc71;\r\n  border: 1px solid rgba(46, 204, 113, 0.2);\r\n}\r\n\r\n/* Duration */\r\n.duration-text {\r\n  color: var(--text-primary);\r\n  font-weight: 500;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n/* Price */\r\n.price-amount {\r\n  color: var(--success);\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n}\r\n\r\n/* Status Badge */\r\n.status-badge {\r\n  display: inline-block;\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: var(--border-radius-full);\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.status-active {\r\n  background-color: rgba(46, 204, 113, 0.1);\r\n  color: #2ecc71;\r\n  border: 1px solid rgba(46, 204, 113, 0.2);\r\n}\r\n\r\n.status-inactive {\r\n  background-color: rgba(149, 165, 166, 0.1);\r\n  color: #95a5a6;\r\n  border: 1px solid rgba(149, 165, 166, 0.2);\r\n}\r\n\r\n/* Action Buttons */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 32px;\r\n  height: 32px;\r\n  border: none;\r\n  border-radius: var(--border-radius-sm);\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  font-size: 0.85rem;\r\n}\r\n\r\n.edit-btn {\r\n  background-color: rgba(52, 152, 219, 0.1);\r\n  color: #3498db;\r\n  border: 1px solid rgba(52, 152, 219, 0.2);\r\n}\r\n\r\n.edit-btn:hover {\r\n  background-color: rgba(52, 152, 219, 0.2);\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.delete-btn {\r\n  background-color: rgba(231, 76, 60, 0.1);\r\n  color: #e74c3c;\r\n  border: 1px solid rgba(231, 76, 60, 0.2);\r\n}\r\n\r\n.delete-btn:hover {\r\n  background-color: rgba(231, 76, 60, 0.2);\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .modern-table {\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .modern-table thead th,\r\n  .modern-table tbody td {\r\n    padding: 0.75rem 0.5rem;\r\n  }\r\n\r\n  .description-text {\r\n    max-width: 150px;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 0.25rem;\r\n  }\r\n\r\n  .action-btn {\r\n    width: 28px;\r\n    height: 28px;\r\n    font-size: 0.8rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 576px) {\r\n  .modern-table {\r\n    font-size: 0.75rem;\r\n  }\r\n\r\n  .modern-table thead th,\r\n  .modern-table tbody td {\r\n    padding: 0.5rem 0.25rem;\r\n  }\r\n\r\n  .description-text {\r\n    max-width: 100px;\r\n  }\r\n\r\n  .package-title {\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .role-badge,\r\n  .status-badge {\r\n    font-size: 0.7rem;\r\n    padding: 0.2rem 0.5rem;\r\n  }\r\n}\r\n\r\n/* Dark Mode Enhancements */\r\n@media (prefers-color-scheme: dark) {\r\n  .modern-table tbody tr:hover {\r\n    background-color: rgba(255, 255, 255, 0.05);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\r\n  }\r\n}"], "mappings": ";AAGA,CAAC;AACC,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,YAAU;AACV,cAAY,IAAI;AAChB,cAAY,IAAI,KAAK;AACvB;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,cAAY;AACd;AAGA,CAAC;AACC,cAAY;AACZ,WAAS,KAAK;AACd,SAAO,IAAI;AACb;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACX;AAEA,CAAC;AACC,SAAO,IAAI;AACX,iBAAe;AACf,eAAa;AACf;AAEA,CAAC;AACC,SAAO,IAAI;AACX,iBAAe;AACf,aAAW;AACb;AAGA,CAAC;AACC,YAAU;AACZ;AAEA,CAAC;AACC,cAAY;AACZ,8BAA4B;AAC9B;AAGA,CAAC;AACC,SAAO;AACP,mBAAiB;AACjB,oBAAkB,IAAI;AACtB,aAAW;AACb;AAEA,CAPC,aAOa;AACZ;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,gBAAgB;AAC7E;AAEA,CAXC,aAWa,MAAM;AAClB,WAAS,KAAK;AACd,cAAY;AACZ,eAAa;AACb,SAAO;AACP,aAAW;AACX,kBAAgB;AAChB,kBAAgB;AAChB,UAAQ;AACR,YAAU;AACZ;AAEA,CAvBC,aAuBa,MAAM,EAAE,KAAK,YAAY;AACrC,WAAS;AACT,YAAU;AACV,SAAO;AACP,OAAK;AACL,UAAQ;AACR,SAAO;AACP,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CAjCC,aAiCa,MAAM;AAClB,cAAY,IAAI,KAAK;AACrB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAtCC,aAsCa,MAAM,EAAE;AACpB,oBAAkB,IAAI;AACtB,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CA5CC,aA4Ca,MAAM,EAAE;AACpB,iBAAe;AACjB;AAEA,CAhDC,aAgDa,MAAM;AAClB,WAAS,KAAK;AACd,kBAAgB;AAChB,UAAQ;AACR,SAAO,IAAI;AACb;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAClB;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,aAAW;AACb;AAGA,CAAC;AACC,SAAO,IAAI;AACX,aAAW;AACX,aAAW;AACX,YAAU;AACV,iBAAe;AACf,eAAa;AACf;AAGA,CAAC;AACC,WAAS;AACT,WAAS,QAAQ;AACjB,iBAAe,IAAI;AACnB,aAAW;AACX,eAAa;AACb,kBAAgB;AAChB,kBAAgB;AAClB;AAEA,CAAC;AACC,oBAAkB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACrC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACvC;AAEA,CAAC;AACC,oBAAkB,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACpC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACtC;AAEA,CAAC;AACC,oBAAkB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACrC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACvC;AAGA,CAAC;AACC,SAAO,IAAI;AACX,eAAa;AACb,aAAW;AACb;AAGA,CAAC;AACC,SAAO,IAAI;AACX,eAAa;AACb,aAAW;AACb;AAGA,CAAC;AACC,WAAS;AACT,WAAS,QAAQ;AACjB,iBAAe,IAAI;AACnB,aAAW;AACX,eAAa;AACb,kBAAgB;AAChB,kBAAgB;AAClB;AAEA,CAAC;AACC,oBAAkB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACrC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACvC;AAEA,CAAC;AACC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAGA,CAAC;AACC,WAAS;AACT,OAAK;AACL,mBAAiB;AACnB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,UAAQ;AACR,UAAQ;AACR,iBAAe,IAAI;AACnB,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,aAAW;AACb;AAEA,CAAC;AACC,oBAAkB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACrC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACvC;AAEA,CANC,QAMQ;AACP,oBAAkB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACrC,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3C;AAEA,CAAC;AACC,oBAAkB,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACpC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACtC;AAEA,CANC,UAMU;AACT,oBAAkB,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACpC,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC1C;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA7LD;AA8LG,eAAW;AACb;AAEA,GAjMD,aAiMe,MAAM;AAAA,EACpB,CAlMD,aAkMe,MAAM;AAClB,aAAS,QAAQ;AACnB;AAEA,GAlID;AAmIG,eAAW;AACb;AAEA,GA1DD;AA2DG,oBAAgB;AAChB,SAAK;AACP;AAEA,GAzDD;AA0DG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAvND;AAwNG,eAAW;AACb;AAEA,GA3ND,aA2Ne,MAAM;AAAA,EACpB,CA5ND,aA4Ne,MAAM;AAClB,aAAS,OAAO;AAClB;AAEA,GA5JD;AA6JG,eAAW;AACb;AAEA,GAvKD;AAwKG,eAAW;AACb;AAEA,GA1JD;AAAA,EA2JC,CAhHD;AAiHG,eAAW;AACX,aAAS,OAAO;AAClB;AACF;AAGA,OAAO,CAAC,oBAAoB,EAAE;AAC5B,GAjPD,aAiPe,MAAM,EAAE;AACpB,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,gBAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AACF;", "names": []}