# Staging Environment Setup Script
# Bu script Staging environment için gerekli environment variables'ları set eder

Write-Host "=== GymKod Staging Environment Setup ===" -ForegroundColor Green
Write-Host "Setting up Staging environment variables..." -ForegroundColor Yellow

# ASPNETCORE Environment
$env:ASPNETCORE_ENVIRONMENT = "Staging"
[System.Environment]::SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Staging", "User")

# Connection String for Staging Database
$connectionString = "Server=localhost;User Id=sa;Password=************;Database=Staging;Trusted_Connection=false;Encrypt=False"
$env:ConnectionStrings__DefaultConnection = $connectionString
[System.Environment]::SetEnvironmentVariable("ConnectionStrings__DefaultConnection", $connectionString, "User")

# Security Key (Production'da farklı olmalı)
$securityKey = "zX9c2Kf8Lm3Qp7Rt5Vw1Hy4Njt6Bd0Gs3Ae8Iu2Zy5Tx7Fq1Cm9Pk4Wv6Hn2Jl8Rd0Se3Gb7Mt"
$env:TokenOptions__SecurityKey = $securityKey
[System.Environment]::SetEnvironmentVariable("TokenOptions__SecurityKey", $securityKey, "User")

Write-Host "✅ Staging environment variables set successfully!" -ForegroundColor Green
Write-Host "Current ASPNETCORE_ENVIRONMENT: $env:ASPNETCORE_ENVIRONMENT" -ForegroundColor Cyan
Write-Host "Connection String configured for: Staging database" -ForegroundColor Cyan
Write-Host "Target URL: staging.gymkod.com" -ForegroundColor Cyan

Write-Host "`n=== Next Steps ===" -ForegroundColor Yellow
Write-Host "1. Restart your IDE/Terminal to pick up new environment variables" -ForegroundColor White
Write-Host "2. Build Frontend: ng build --configuration=staging" -ForegroundColor White
Write-Host "3. Run Backend: dotnet run --project WebAPI" -ForegroundColor White
Write-Host "4. Deploy to staging.gymkod.com" -ForegroundColor White

pause
