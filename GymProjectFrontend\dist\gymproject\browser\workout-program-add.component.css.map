{"version": 3, "sources": ["src/app/components/workout-programs/workout-program-add.component.css"], "sourcesContent": ["/* Workout Program Add Wizard Styles */\n\n/* Header */\n.sticky-header {\n  position: relative;\n  background-color: var(--bg-primary);\n  border-bottom: 1px solid var(--border-color);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n[data-theme=\"dark\"] .sticky-header {\n  background-color: var(--bg-primary);\n  border-bottom-color: var(--border-color);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.main-content {\n  padding-top: 0;\n}\n\n/* Progress Bar Enhancements */\n.progress {\n  background-color: rgba(var(--primary-rgb), 0.1);\n  border-radius: var(--border-radius-pill);\n}\n\n.progress-bar {\n  transition: width 0.6s ease;\n  border-radius: var(--border-radius-pill);\n}\n\n/* Step Navigation Buttons */\n.btn {\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* Day Count Selection Cards */\n.form-check {\n  margin-bottom: 0;\n}\n\n.form-check-input[type=\"radio\"] {\n  margin-top: 0;\n}\n\n.form-check-label .modern-card {\n  transition: all var(--transition-speed) var(--transition-timing);\n  border: 2px solid transparent;\n}\n\n.form-check-input:checked + .form-check-label .modern-card {\n  border-color: var(--primary);\n  background-color: var(--primary-light);\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-md);\n}\n\n.form-check-label:hover .modern-card {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-sm);\n}\n\n/* Day Cards */\n.day-card {\n  border: 1px solid var(--border-color);\n  border-radius: var(--border-radius-lg);\n  background-color: var(--bg-primary);\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n.day-card:hover {\n  box-shadow: var(--shadow-sm);\n  transform: translateY(-2px);\n}\n\n.day-header {\n  padding: 1rem 1.25rem;\n  border-bottom: 1px solid var(--border-color);\n  background-color: var(--bg-secondary);\n  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;\n}\n\n.day-body {\n  padding: 1.25rem;\n}\n\n.day-number {\n  font-weight: 600;\n  color: var(--primary);\n}\n\n.form-check-input:checked {\n  background-color: var(--primary);\n  border-color: var(--primary);\n}\n\n.form-check-input:focus {\n  border-color: var(--primary);\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem var(--primary-light);\n}\n\n.form-check-label {\n  font-weight: 500;\n  margin-left: 0.5rem;\n}\n\n.is-invalid {\n  border-color: var(--danger);\n}\n\n.invalid-feedback {\n  display: block;\n  width: 100%;\n  margin-top: 0.25rem;\n  font-size: 0.875rem;\n  color: var(--danger);\n}\n\n.alert {\n  padding: 0.75rem 1rem;\n  margin-bottom: 0;\n  border: 1px solid transparent;\n  border-radius: var(--border-radius-md);\n}\n\n.alert-info {\n  color: var(--info);\n  background-color: var(--info-light);\n  border-color: rgba(var(--info-rgb), 0.2);\n}\n\n.alert-warning {\n  color: var(--warning);\n  background-color: var(--warning-light);\n  border-color: rgba(var(--warning-rgb), 0.2);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .sticky-header .modern-card-header {\n    padding: 0.75rem 1rem;\n  }\n\n  .sticky-header .modern-card-header .d-flex.gap-2 {\n    flex-direction: row;\n    gap: 0.25rem !important;\n  }\n\n  .sticky-header .modern-btn {\n    padding: 0.375rem 0.75rem;\n    font-size: 0.875rem;\n  }\n\n  .sticky-header h4 {\n    font-size: 1.1rem;\n  }\n\n  .sticky-header small {\n    font-size: 0.75rem;\n  }\n\n  .day-header {\n    padding: 0.75rem 1rem;\n  }\n\n  .day-body {\n    padding: 1rem;\n  }\n\n  .modern-btn-sm {\n    padding: 0.25rem 0.5rem;\n    font-size: 0.75rem;\n  }\n\n  .d-flex.gap-2:not(.sticky-header .d-flex.gap-2) {\n    flex-direction: column;\n    gap: 0.5rem !important;\n  }\n}\n\n/* Dark mode specific adjustments */\n[data-theme=\"dark\"] .day-card {\n  background-color: var(--bg-secondary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .day-header {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .form-check-input {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .form-check-input:checked {\n  background-color: var(--primary);\n  border-color: var(--primary);\n}\n\n[data-theme=\"dark\"] .alert-info {\n  background-color: var(--info-light);\n  border-color: rgba(var(--info-rgb), 0.3);\n  color: var(--info);\n}\n\n[data-theme=\"dark\"] .alert-warning {\n  background-color: var(--warning-light);\n  border-color: rgba(var(--warning-rgb), 0.3);\n  color: var(--warning);\n}\n\n/* Wizard Step Animations */\n.fade-in {\n  animation: fadeInUp 0.5s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Exercise Setup Cards */\n.exercise-setup-card {\n  transition: all var(--transition-speed) var(--transition-timing);\n  cursor: pointer;\n}\n\n.exercise-setup-card:hover {\n  transform: translateY(-3px);\n  box-shadow: var(--shadow-md);\n}\n\n.exercise-setup-card.configured {\n  border-color: var(--success);\n  background-color: var(--success-light);\n}\n\n.exercise-setup-card.not-configured {\n  border-color: var(--warning);\n  background-color: var(--warning-light);\n}\n\n/* Preview Section */\n.preview-summary {\n  background: linear-gradient(135deg, var(--primary-light), var(--info-light));\n  border-radius: var(--border-radius-lg);\n  padding: var(--spacing-lg);\n  margin-bottom: var(--spacing-lg);\n}\n\n/* Step Navigation Improvements */\n.step-nav-btn {\n  min-height: 80px;\n  text-align: left;\n  border: 2px solid transparent;\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n.step-nav-btn.active {\n  border-color: var(--primary);\n  background-color: var(--primary);\n  color: white;\n}\n\n.step-nav-btn.completed {\n  border-color: var(--success);\n  background-color: var(--success-light);\n  color: var(--success);\n}\n\n.step-nav-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-sm);\n}\n\n.step-nav-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* Dark Mode Enhancements */\n[data-theme=\"dark\"] .form-check-input:checked + .form-check-label .modern-card {\n  background-color: var(--primary-light);\n  border-color: var(--primary);\n}\n\n[data-theme=\"dark\"] .preview-summary {\n  background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.2), rgba(var(--info-rgb), 0.2));\n}\n\n[data-theme=\"dark\"] .step-nav-btn.completed {\n  background-color: var(--success-light);\n  color: var(--success);\n}\n\n/* Responsive Improvements */\n@media (max-width: 768px) {\n  .step-nav-btn {\n    min-height: 60px;\n    font-size: 0.875rem;\n  }\n\n  .step-nav-btn .fw-bold {\n    font-size: 0.8rem;\n  }\n\n  .step-nav-btn small {\n    font-size: 0.7rem;\n  }\n\n  .modern-card-body {\n    padding: var(--spacing-md);\n  }\n\n  .row.g-3 {\n    --bs-gutter-x: 1rem;\n    --bs-gutter-y: 1rem;\n  }\n}\n\n/* Titreşim animasyonu için sınıf */\n.shake-animation {\n  animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;\n  transform: translate3d(0, 0, 0);\n  backface-visibility: hidden;\n  perspective: 1000px;\n  border-color: #dc3545 !important;\n  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5) !important;\n}\n\n@keyframes shake {\n  0%, 100% { transform: translateX(0); }\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-6px); }\n  20%, 40%, 60%, 80% { transform: translateX(6px); }\n}\n"], "mappings": ";AAGA,CAAC;AACC,YAAU;AACV,oBAAkB,IAAI;AACtB,iBAAe,IAAI,MAAM,IAAI;AAC7B,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAAC,iBAAmB,CARnB;AASC,oBAAkB,IAAI;AACtB,uBAAqB,IAAI;AACzB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC;AACC,eAAa;AACf;AAGA,CAAC;AACC,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,cAAY,MAAM,KAAK;AACvB,iBAAe,IAAI;AACrB;AAGA,CAAC;AACC,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAJC,GAIG;AACF,WAAS;AACT,UAAQ;AACV;AAGA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC,gBAAgB,CAAC;AAChB,cAAY;AACd;AAEA,CAAC,iBAAiB,CAAC;AACjB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,UAAQ,IAAI,MAAM;AACpB;AAEA,CATC,gBASgB,SAAS,EAAE,CAL3B,iBAK6C,CAL3B;AAMjB,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACtB,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAZC,gBAYgB,OAAO,CAZL;AAajB,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAGA,CAAC;AACC,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAPC,QAOQ;AACP,cAAY,IAAI;AAChB,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,WAAS,KAAK;AACd,iBAAe,IAAI,MAAM,IAAI;AAC7B,oBAAkB,IAAI;AACtB,iBAAe,IAAI,oBAAoB,IAAI,oBAAoB,EAAE;AACnE;AAEA,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAlDC,gBAkDgB;AACf,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAvDC,gBAuDgB;AACf,gBAAc,IAAI;AAClB,WAAS;AACT,cAAY,EAAE,EAAE,EAAE,QAAQ,IAAI;AAChC;AAEA,CAzDC;AA0DC,eAAa;AACb,eAAa;AACf;AAEA,CAAC;AACC,gBAAc,IAAI;AACpB;AAEA,CAAC;AACC,WAAS;AACT,SAAO;AACP,cAAY;AACZ,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,iBAAe;AACf,UAAQ,IAAI,MAAM;AAClB,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,gBAAc,KAAK,IAAI,WAAW,EAAE;AACtC;AAEA,CAAC;AACC,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,gBAAc,KAAK,IAAI,cAAc,EAAE;AACzC;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA/ID,cA+IgB,CAAC;AACd,aAAS,QAAQ;AACnB;AAEA,GAnJD,cAmJgB,CAJC,mBAImB,CAAC,MAAM,CAAC;AACzC,oBAAgB;AAChB,SAAK;AACP;AAEA,GAxJD,cAwJgB,CAAC;AACd,aAAS,SAAS;AAClB,eAAW;AACb;AAEA,GA7JD,cA6JgB;AACb,eAAW;AACb;AAEA,GAjKD,cAiKgB;AACb,eAAW;AACb;AAEA,GAvFD;AAwFG,aAAS,QAAQ;AACnB;AAEA,GApFD;AAqFG,aAAS;AACX;AAEA,GAAC;AACC,aAAS,QAAQ;AACjB,eAAW;AACb;AAEA,GA/BoC,MA+B7B,CA/BoC,KA+B9B,KAAK,CAlLnB,cAkLkC,CA/BG,MA+BI,CA/BG;AAgCzC,oBAAgB;AAChB,SAAK;AACP;AACF;AAGA,CAAC,iBAAmB,CAvHnB;AAwHC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAhHnB;AAiHC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAvJnB;AAwJC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA5JnB,gBA4JoC;AACnC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA5EnB;AA6EC,oBAAkB,IAAI;AACtB,gBAAc,KAAK,IAAI,WAAW,EAAE;AACpC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA5EnB;AA6EC,oBAAkB,IAAI;AACtB,gBAAc,KAAK,IAAI,cAAc,EAAE;AACvC,SAAO,IAAI;AACb;AAGA,CAAC;AACC,aAAW,SAAS,KAAK;AAC3B;AAEA,WAHa;AAIX;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACA;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACF;AAGA,CAAC;AACC,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,UAAQ;AACV;AAEA,CALC,mBAKmB;AAClB,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAVC,mBAUmB,CAAC;AACnB,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACxB;AAEA,CAfC,mBAemB,CAAC;AACnB,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACxB;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,gBAAgB;AAAA,MAAE,IAAI;AAC9D,iBAAe,IAAI;AACnB,WAAS,IAAI;AACb,iBAAe,IAAI;AACrB;AAGA,CAAC;AACC,cAAY;AACZ,cAAY;AACZ,UAAQ,IAAI,MAAM;AAClB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAPC,YAOY,CAAC;AACZ,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACtB,SAAO;AACT;AAEA,CAbC,YAaY,CAAC;AACZ,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAnBC,YAmBY,MAAM,KAAK;AACtB,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAxBC,YAwBY;AACX,WAAS;AACT,UAAQ;AACV;AAGA,CAAC,iBAAmB,CAzPnB,gBAyPoC,SAAS,EAAE,CArP/C,iBAqPiE,CArP/C;AAsPjB,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA3CnB;AA4CC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,IAAI,cAAc,EAAE,IAAjD;AAAA,MAAuD,KAAK,IAAI,WAAW,EAAE;AAC3F;AAEA,CAAC,iBAAmB,CAvCnB,YAuCgC,CA1BnB;AA2BZ,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA9CD;AA+CG,gBAAY;AACZ,eAAW;AACb;AAEA,GAnDD,aAmDe,CAAC;AACb,eAAW;AACb;AAEA,GAvDD,aAuDe;AACZ,eAAW;AACb;AAEA,GAAC;AACC,aAAS,IAAI;AACf;AAEA,GAAC,GAAG,CAAC;AACH,mBAAe;AACf,mBAAe;AACjB;AACF;AAGA,CAAC;AACC,aAAW,MAAM,KAAK,aAAa,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK;AACpD,aAAW,YAAY,CAAC,EAAE,CAAC,EAAE;AAC7B,uBAAqB;AACrB,eAAa;AACb,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,QAAQ,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC9C;AAEA,WARa;AASX;AAAW,eAAW,WAAW;AAAI;AACrC;AAA0B,eAAW,WAAW;AAAO;AACvD;AAAqB,eAAW,WAAW;AAAM;AACnD;", "names": []}