{"version": 3, "sources": ["src/app/components/product-list/product-list.component.css"], "sourcesContent": ["/* Product List Component Styles */\r\n\r\n/* Modern Sort Button */\r\n.modern-sort-btn {\r\n  background: var(--bg-primary);\r\n  border: 1px solid var(--border-color);\r\n  border-radius: 8px;\r\n  color: var(--text-primary);\r\n  padding: 8px 12px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.modern-sort-btn:hover {\r\n  background: var(--bg-secondary);\r\n  border-color: var(--primary);\r\n  color: var(--primary);\r\n}\r\n\r\n.modern-sort-btn:focus {\r\n  outline: none;\r\n  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);\r\n  border-color: var(--primary);\r\n}\r\n\r\n.sort-text {\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.sort-badge {\r\n  background: var(--primary);\r\n  color: white;\r\n  padding: 2px 6px;\r\n  border-radius: 6px;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  margin-left: 4px;\r\n}\r\n\r\n/* Modern Sort Dropdown */\r\n.modern-sort-dropdown {\r\n  border: 1px solid var(--border-color);\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  padding: 4px;\r\n  margin-top: 4px;\r\n  background: var(--bg-primary);\r\n  min-width: 180px;\r\n}\r\n\r\n.modern-sort-item {\r\n  border-radius: 6px;\r\n  padding: 8px 12px;\r\n  margin-bottom: 2px;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  color: var(--text-primary);\r\n  text-decoration: none;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.modern-sort-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.modern-sort-item:hover {\r\n  background: var(--bg-secondary);\r\n  color: var(--text-primary);\r\n  text-decoration: none;\r\n}\r\n\r\n.modern-sort-item.active {\r\n  background: var(--primary);\r\n  color: white;\r\n}\r\n\r\n.modern-sort-item.active:hover {\r\n  background: var(--primary-dark);\r\n  color: white;\r\n  text-decoration: none;\r\n}\r\n\r\n/* Dark theme support */\r\n[data-theme=\"dark\"] .modern-sort-dropdown {\r\n  background: var(--bg-primary);\r\n  border-color: var(--border-color);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-sort-btn {\r\n  background: var(--bg-primary);\r\n  border-color: var(--border-color);\r\n  color: var(--text-primary);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-sort-btn:hover {\r\n  background: var(--bg-secondary);\r\n  border-color: var(--primary);\r\n}"], "mappings": ";AAGA,CAAC;AACC,cAAY,IAAI;AAChB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe;AACf,SAAO,IAAI;AACX,WAAS,IAAI;AACb,eAAa;AACb,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,eAAa;AACb,OAAK;AACL,aAAW;AACb;AAEA,CAdC,eAce;AACd,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CApBC,eAoBe;AACd,WAAS;AACT,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,IAAI,cAAc,EAAE;AAC/C,gBAAc,IAAI;AACpB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,SAAO;AACP,WAAS,IAAI;AACb,iBAAe;AACf,aAAW;AACX,eAAa;AACb,eAAa;AACf;AAGA,CAAC;AACC,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe;AACf,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrC,WAAS;AACT,cAAY;AACZ,cAAY,IAAI;AAChB,aAAW;AACb;AAEA,CAAC;AACC,iBAAe;AACf,WAAS,IAAI;AACb,iBAAe;AACf,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,eAAa;AACb,SAAO,IAAI;AACX,mBAAiB;AACjB,aAAW;AACb;AAEA,CAZC,gBAYgB;AACf,iBAAe;AACjB;AAEA,CAhBC,gBAgBgB;AACf,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,mBAAiB;AACnB;AAEA,CAtBC,gBAsBgB,CAAC;AAChB,cAAY,IAAI;AAChB,SAAO;AACT;AAEA,CA3BC,gBA2BgB,CALC,MAKM;AACtB,cAAY,IAAI;AAChB,SAAO;AACP,mBAAiB;AACnB;AAGA,CAAC,iBAAmB,CA5CnB;AA6CC,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAAC,iBAAmB,CA5FnB;AA6FC,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAlGnB,eAkGmC;AAClC,cAAY,IAAI;AAChB,gBAAc,IAAI;AACpB;", "names": []}