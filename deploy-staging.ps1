# Staging Deployment Script - Ana Klasörden Çalıştır
# Bu script Staging environment için tam otomatik deployment yapar

param(
    [switch]$SkipBuild = $false,
    [switch]$SkipFrontend = $false
)

Write-Host "=== GymKod Staging Deployment ===" -ForegroundColor Green
Write-Host "Starting Staging deployment process..." -ForegroundColor Yellow
Write-Host "Target: staging.gymkod.com" -ForegroundColor Cyan

# Set Environment
Write-Host "`n1. Setting Environment Variables..." -ForegroundColor Cyan
$env:ASPNETCORE_ENVIRONMENT = "Staging"
$env:ConnectionStrings__DefaultConnection = "Server=localhost;User Id=sa;Password=************;Database=Staging;Trusted_Connection=false;Encrypt=False"

Write-Host "✅ Environment set to: Staging" -ForegroundColor Green

# Backend Build
if (-not $SkipBuild) {
    Write-Host "`n2. Building Backend..." -ForegroundColor Cyan
    Set-Location "GymProjectBackend"
    
    Write-Host "Restoring packages..." -ForegroundColor Yellow
    dotnet restore
    
    Write-Host "Building solution for Staging..." -ForegroundColor Yellow
    dotnet build --configuration Release
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Backend build failed!" -ForegroundColor Red
        Set-Location ".."
        pause
        exit 1
    }
    
    Write-Host "✅ Backend build successful!" -ForegroundColor Green
    Set-Location ".."
}

# Frontend Build
if (-not $SkipFrontend) {
    Write-Host "`n3. Building Frontend..." -ForegroundColor Cyan
    Set-Location "GymProjectFrontend"
    
    Write-Host "Installing npm packages..." -ForegroundColor Yellow
    npm install
    
    Write-Host "Building Angular app for Staging..." -ForegroundColor Yellow
    ng build --configuration=staging
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Frontend build failed!" -ForegroundColor Red
        Set-Location ".."
        pause
        exit 1
    }
    
    Write-Host "✅ Frontend build successful!" -ForegroundColor Green
    Set-Location ".."
}

Write-Host "`n4. Deployment Ready..." -ForegroundColor Cyan
Write-Host "Files are ready for deployment to staging.gymkod.com" -ForegroundColor Yellow

Write-Host "`n=== Staging Deployment Complete! ===" -ForegroundColor Green
Write-Host "✅ Environment: Staging" -ForegroundColor Green
Write-Host "✅ Database: Staging" -ForegroundColor Green
Write-Host "✅ API URL: https://staging.gymkod.com/api/" -ForegroundColor Green
Write-Host "✅ Frontend URL: https://staging.gymkod.com" -ForegroundColor Green

Write-Host "`nNext Steps:" -ForegroundColor Yellow
Write-Host "1. Copy backend files to staging server" -ForegroundColor White
Write-Host "2. Copy frontend dist files to staging web directory" -ForegroundColor White
Write-Host "3. Restart staging server" -ForegroundColor White
Write-Host "4. Test on staging.gymkod.com" -ForegroundColor White

Write-Host "`n=== Quick Start Commands ===" -ForegroundColor Yellow
Write-Host "To run locally with Staging config:" -ForegroundColor White
Write-Host "Backend: cd GymProjectBackend && dotnet run --project WebAPI" -ForegroundColor Cyan
Write-Host "Frontend: cd GymProjectFrontend && ng serve --configuration=staging" -ForegroundColor Cyan

pause
