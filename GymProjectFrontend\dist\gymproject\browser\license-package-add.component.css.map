{"version": 3, "sources": ["src/app/components/crud/license-package-add/license-package-add.component.css"], "sourcesContent": ["/* License Package Add Component Styles */\n\n/* Content Blur Effect */\n.content-blur {\n  filter: blur(3px);\n  pointer-events: none;\n}\n\n/* Form Section */\n.form-section {\n  margin-bottom: 1.5rem;\n  padding: 1.25rem;\n  background-color: var(--bg-secondary);\n  border-radius: var(--border-radius-md);\n  transition: all 0.3s ease;\n}\n\n.form-section:hover {\n  background-color: var(--bg-tertiary);\n  box-shadow: var(--shadow-sm);\n}\n\n.section-title {\n  font-weight: 600;\n  color: var(--primary);\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n}\n\n/* Animations */\n.fade-in {\n  animation: fadeIn 0.5s var(--transition-timing);\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(10px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n.slide-in-right {\n  animation: slideInRight 0.5s var(--transition-timing);\n}\n\n@keyframes slideInRight {\n  from { opacity: 0; transform: translateX(20px); }\n  to { opacity: 1; transform: translateX(0); }\n}\n\n/* Loading Overlay */\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.spinner-container {\n  background-color: var(--bg-primary);\n  padding: 2rem;\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-lg);\n}\n\n/* Dark Mode Support */\n@media (prefers-color-scheme: dark) {\n  .form-section {\n    background-color: rgba(255, 255, 255, 0.05);\n  }\n  \n  .form-section:hover {\n    background-color: rgba(255, 255, 255, 0.08);\n  }\n}\n\n/* Fix for price input group */\n.input-group {\n  display: flex;\n  align-items: center;\n  height: 38px; /* Set a fixed height to match input height */\n}\n\n.input-group .modern-form-control {\n  flex: 1;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.input-group .input-group-text {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.375rem 0.75rem;\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  text-align: center;\n  white-space: nowrap;\n  border-top-left-radius: var(--border-radius-sm);\n  border-bottom-left-radius: var(--border-radius-sm);\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n  height: 100%;\n  background-color: var(--bg-tertiary);\n  width: 40px; /* Fixed width for the currency symbol */\n  border: 1px solid var(--border-color);\n  border-right: none;\n  color: var(--text-secondary);\n}\n\n/* Textarea styling */\n.modern-form-control[rows] {\n  resize: vertical;\n  min-height: 80px;\n}\n\n/* Checkbox styling */\n.form-check {\n  display: flex;\n  align-items: center;\n  margin-top: 0.5rem;\n}\n\n.form-check-input {\n  margin-right: 0.5rem;\n  width: 1.2em;\n  height: 1.2em;\n}\n\n.form-check-label {\n  font-size: 0.95rem;\n  color: var(--text-primary);\n  cursor: pointer;\n}\n\n/* Responsive Adjustments */\n@media (max-width: 767.98px) {\n  .validity-selects {\n    flex-direction: column;\n  }\n  \n  .validity-selects select {\n    margin-right: 0 !important;\n    margin-bottom: 0.5rem;\n  }\n  \n  .validity-selects select:last-child {\n    margin-bottom: 0;\n  }\n  \n  /* Ensure input group stays horizontal even on mobile */\n  .input-group {\n    flex-direction: row;\n  }\n}\n"], "mappings": ";AAGA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;AAGA,CAAC;AACC,iBAAe;AACf,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI,KAAK;AACvB;AAEA,CARC,YAQY;AACX,oBAAkB,IAAI;AACtB,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACT,eAAa;AACf;AAGA,CAAC;AACC,aAAW,OAAO,KAAK,IAAI;AAC7B;AAEA,WAHa;AAIX;AAAO,aAAS;AAAG,eAAW,WAAW;AAAO;AAChD;AAAK,aAAS;AAAG,eAAW,WAAW;AAAI;AAC7C;AAEA,CAAC;AACC,aAAW,aAAa,KAAK,IAAI;AACnC;AAEA,WAHa;AAIX;AAAO,aAAS;AAAG,eAAW,WAAW;AAAO;AAChD;AAAK,aAAS;AAAG,eAAW,WAAW;AAAI;AAC7C;AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACX;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,WAAS;AACT,iBAAe,IAAI;AACnB,cAAY,IAAI;AAClB;AAGA,OAAO,CAAC,oBAAoB,EAAE;AAC5B,GA/DD;AAgEG,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,GAnED,YAmEc;AACX,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AACF;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,UAAQ;AACV;AAEA,CANC,YAMY,CAAC;AACZ,QAAM;AACN,0BAAwB;AACxB,6BAA2B;AAC7B;AAEA,CAZC,YAYY,CAAC;AACZ,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS,SAAS;AAClB,aAAW;AACX,eAAa;AACb,eAAa;AACb,cAAY;AACZ,eAAa;AACb,0BAAwB,IAAI;AAC5B,6BAA2B,IAAI;AAC/B,2BAAyB;AACzB,8BAA4B;AAC5B,UAAQ;AACR,oBAAkB,IAAI;AACtB,SAAO;AACP,UAAQ,IAAI,MAAM,IAAI;AACtB,gBAAc;AACd,SAAO,IAAI;AACb;AAGA,CA7Bc,mBA6BM,CAAC;AACnB,UAAQ;AACR,cAAY;AACd;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,cAAY;AACd;AAEA,CAAC;AACC,gBAAc;AACd,SAAO;AACP,UAAQ;AACV;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,UAAQ;AACV;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,oBAAgB;AAClB;AAEA,GAJC,iBAIiB;AAChB,kBAAc;AACd,mBAAe;AACjB;AAEA,GATC,iBASiB,MAAM;AACtB,mBAAe;AACjB;AAGA,GA3ED;AA4EG,oBAAgB;AAClB;AACF;", "names": []}