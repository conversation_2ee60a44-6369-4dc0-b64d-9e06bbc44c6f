# Production Environment Setup Script
# Bu script Production environment için gerekli environment variables'ları set eder

Write-Host "=== GymKod Production Environment Setup ===" -ForegroundColor Red
Write-Host "⚠️  WARNING: You are setting up PRODUCTION environment!" -ForegroundColor Red
Write-Host "This will connect to the LIVE database with 1000+ gym users!" -ForegroundColor Red

$confirmation = Read-Host "Are you sure you want to continue? Type 'YES' to confirm"
if ($confirmation -ne "YES") {
    Write-Host "❌ Production setup cancelled." -ForegroundColor Yellow
    pause
    exit
}

Write-Host "Setting up Production environment variables..." -ForegroundColor Yellow

# ASPNETCORE Environment
$env:ASPNETCORE_ENVIRONMENT = "Production"
[System.Environment]::SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Production", "User")

# Connection String for Production Database
$connectionString = "Server=localhost;User Id=sa;Password=************;Database=GymProject;Trusted_Connection=false;Encrypt=False"
$env:ConnectionStrings__DefaultConnection = $connectionString
[System.Environment]::SetEnvironmentVariable("ConnectionStrings__DefaultConnection", $connectionString, "User")

# Production Security Key (Farklı bir key kullanmalısın!)
$securityKey = "zX9c2Kf8Lm3Qp7Rt5Vw1Hy4Njt6Bd0Gs3Ae8Iu2Zy5Tx7Fq1Cm9Pk4Wv6Hn2Jl8Rd0Se3Gb7Mt"
$env:TokenOptions__SecurityKey = $securityKey
[System.Environment]::SetEnvironmentVariable("TokenOptions__SecurityKey", $securityKey, "User")

Write-Host "✅ Production environment variables set successfully!" -ForegroundColor Green
Write-Host "Current ASPNETCORE_ENVIRONMENT: $env:ASPNETCORE_ENVIRONMENT" -ForegroundColor Cyan
Write-Host "Connection String configured for: GymProject (PRODUCTION) database" -ForegroundColor Red
Write-Host "Target URL: admin.gymkod.com" -ForegroundColor Cyan

Write-Host "`n=== Next Steps ===" -ForegroundColor Yellow
Write-Host "1. Restart your IDE/Terminal to pick up new environment variables" -ForegroundColor White
Write-Host "2. Build Frontend: ng build --configuration=production" -ForegroundColor White
Write-Host "3. Run Backend: dotnet run --project WebAPI" -ForegroundColor White
Write-Host "4. Deploy to admin.gymkod.com" -ForegroundColor White
Write-Host "5. ⚠️  DOUBLE CHECK: You are now connected to LIVE database!" -ForegroundColor Red

pause
