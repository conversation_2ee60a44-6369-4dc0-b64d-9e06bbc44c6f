{"version": 3, "sources": ["src/app/components/transaction-list/transaction-list.component.css"], "sourcesContent": ["/* Transaction List Component Styles */\r\n\r\n.content-blur {\r\n  filter: blur(2px);\r\n  pointer-events: none;\r\n}\r\n\r\n/* Transaction Summary Cards */\r\n/* Daily Transaction Card (Info color) */\r\n.daily-transaction-card {\r\n  background: linear-gradient(135deg, var(--info-light) 0%, rgba(var(--info-rgb), 0.2) 100%);\r\n}\r\n.daily-transaction-card .modern-stats-icon {\r\n  background: linear-gradient(135deg, var(--info) 0%, #0b7b9a 100%);\r\n}\r\n.daily-transaction-card .modern-stats-value {\r\n  color: var(--info);\r\n}\r\n\r\n/* Monthly Transaction Card (Primary color) */\r\n.monthly-transaction-card {\r\n  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(var(--primary-rgb), 0.2) 100%);\r\n}\r\n.monthly-transaction-card .modern-stats-icon {\r\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\r\n}\r\n.monthly-transaction-card .modern-stats-value {\r\n  color: var(--primary);\r\n}\r\n\r\n/* Filtered Transaction Card (Warning color - özel vurgu için) */\r\n.filtered-transaction-card {\r\n  background: linear-gradient(135deg, var(--warning-light) 0%, rgba(var(--warning-rgb), 0.2) 100%);\r\n  border: 2px solid var(--warning);\r\n  box-shadow: 0 8px 25px rgba(var(--warning-rgb), 0.15);\r\n}\r\n.filtered-transaction-card .modern-stats-icon {\r\n  background: linear-gradient(135deg, var(--warning) 0%, #e0a800 100%);\r\n}\r\n.filtered-transaction-card .modern-stats-value {\r\n  color: var(--warning);\r\n  font-weight: 700;\r\n}\r\n.filtered-transaction-card .modern-stats-label {\r\n  font-weight: 600;\r\n}\r\n.filtered-transaction-card small {\r\n  font-weight: 500;\r\n}\r\n\r\n/* Page Header */\r\n.page-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 1.5rem;\r\n}\r\n\r\n/* Search Container */\r\n.search-container {\r\n  position: relative;\r\n}\r\n\r\n.search-container .form-control {\r\n  padding-left: 2.5rem;\r\n  border-radius: 8px;\r\n  border: 1px solid var(--border-color);\r\n  background: var(--card-bg-color);\r\n  color: var(--text-color);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-container .form-control:focus {\r\n  border-color: var(--primary-color);\r\n  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color), 0.25);\r\n  background: var(--card-bg-color);\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 0.75rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: var(--text-muted);\r\n  z-index: 10;\r\n}\r\n\r\n/* Member Avatar */\r\n.member-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Member Card */\r\n.member-card {\r\n  margin-bottom: 1rem;\r\n  border: 1px solid var(--border-color);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.member-card:hover {\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Member Card Header Layout */\r\n.member-card .modern-card-header {\r\n  padding: 1.25rem;\r\n}\r\n\r\n.member-card .modern-card-header .row {\r\n  margin: 0;\r\n  width: 100%;\r\n}\r\n\r\n.member-card .modern-card-header .row > div {\r\n  padding: 0 0.5rem;\r\n}\r\n\r\n/* Member Info Section */\r\n.member-card h6 {\r\n  color: var(--text-color);\r\n  margin-bottom: 0.25rem;\r\n}\r\n\r\n.member-card .text-muted {\r\n  color: var(--text-muted) !important;\r\n}\r\n\r\n/* Debt Info Styling */\r\n.member-card .text-danger {\r\n  color: var(--danger-color) !important;\r\n  font-weight: 700;\r\n}\r\n\r\n/* Button Styling in Member Card */\r\n.member-card .btn-success {\r\n  background: var(--success-color);\r\n  border-color: var(--success-color);\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.member-card .btn-success:hover {\r\n  background: var(--success-hover-color);\r\n  border-color: var(--success-hover-color);\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(var(--success-color), 0.3);\r\n}\r\n\r\n/* Empty State */\r\n.empty-state {\r\n  padding: 3rem 1rem;\r\n}\r\n\r\n.empty-state i {\r\n  opacity: 0.5;\r\n}\r\n\r\n/* Transaction Group */\r\n.member-transaction-group {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n/* Button Group Toggle */\r\n.btn-group .btn {\r\n  border-radius: 0;\r\n  border-color: var(--border-color);\r\n  background: var(--card-bg-color);\r\n  color: var(--text-color);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.btn-group .btn:first-child {\r\n  border-top-left-radius: 8px;\r\n  border-bottom-left-radius: 8px;\r\n}\r\n\r\n.btn-group .btn:last-child {\r\n  border-top-right-radius: 8px;\r\n  border-bottom-right-radius: 8px;\r\n}\r\n\r\n.btn-group .btn.active {\r\n  background: var(--primary-color);\r\n  border-color: var(--primary-color);\r\n  color: white;\r\n  box-shadow: 0 2px 8px rgba(var(--primary-color), 0.3);\r\n}\r\n\r\n.btn-group .btn:hover:not(.active) {\r\n  background: var(--hover-bg-color);\r\n  border-color: var(--primary-color);\r\n  color: var(--primary-color);\r\n}\r\n\r\n.btn-group .btn .badge {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: inherit;\r\n}\r\n\r\n.btn-group .btn:not(.active) .badge {\r\n  background: var(--secondary-color);\r\n  color: white;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .page-icon {\r\n    width: 40px;\r\n    height: 40px;\r\n    font-size: 1.2rem;\r\n  }\r\n\r\n  .member-avatar {\r\n    width: 32px;\r\n    height: 32px;\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .modern-stats-card {\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .btn-group {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .btn-group .btn {\r\n    border-radius: 8px !important;\r\n    margin-bottom: 0.5rem;\r\n  }\r\n\r\n  .btn-group .btn:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  /* Member Card Mobile Layout */\r\n  .member-card .modern-card-header .row {\r\n    margin: 0;\r\n  }\r\n\r\n  .member-card .modern-card-header .row > div {\r\n    padding: 0.5rem 0;\r\n  }\r\n\r\n  /* Mobile'da orta kısım gizlenir, sadece sol ve sağ görünür */\r\n  .member-card .modern-card-header .col-lg-4.col-md-3 {\r\n    display: none;\r\n  }\r\n\r\n  /* Sağ taraftaki buton ve badge'i alt satıra al */\r\n  .member-card .modern-card-header .col-lg-4.col-md-4.col-12 {\r\n    margin-top: 0.5rem;\r\n  }\r\n\r\n  .member-card .modern-card-header .col-lg-4.col-md-4.col-12 .d-flex {\r\n    justify-content: space-between;\r\n  }\r\n}\r\n\r\n/* Tablet Layout */\r\n@media (max-width: 992px) and (min-width: 769px) {\r\n  .member-card .modern-card-header .col-lg-4.col-md-3 {\r\n    text-align: left;\r\n  }\r\n\r\n  .member-card .modern-card-header .col-lg-4.col-md-3 .d-flex {\r\n    justify-content: flex-start;\r\n  }\r\n}\r\n\r\n@media (max-width: 576px) {\r\n  .modern-stats-card .modern-stats-value {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .modern-stats-card .modern-stats-label {\r\n    font-size: 0.75rem;\r\n  }\r\n\r\n  .modern-table {\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .modern-table th,\r\n  .modern-table td {\r\n    padding: 0.5rem 0.25rem;\r\n  }\r\n\r\n  .modern-badge {\r\n    font-size: 0.65rem;\r\n    padding: 0.25rem 0.5rem;\r\n  }\r\n\r\n  /* Çok küçük ekranlarda buton yazısını kısalt */\r\n  .btn-sm .fas.fa-money-bill-wave + span {\r\n    display: none;\r\n  }\r\n\r\n  .btn-sm::after {\r\n    content: \"Öde\";\r\n  }\r\n}\r\n\r\n/* Dark Theme Support */\r\n[data-theme=\"dark\"] .search-container .form-control {\r\n  background: var(--card-bg-color);\r\n  border-color: var(--border-color);\r\n  color: var(--text-color);\r\n}\r\n\r\n[data-theme=\"dark\"] .search-container .form-control:focus {\r\n  border-color: var(--primary-color);\r\n  background: var(--card-bg-color);\r\n}\r\n\r\n[data-theme=\"dark\"] .member-card {\r\n  background: var(--card-bg-color);\r\n  border-color: var(--border-color);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n[data-theme=\"dark\"] .btn-group .btn {\r\n  background: var(--card-bg-color);\r\n  border-color: var(--border-color);\r\n  color: var(--text-color);\r\n}\r\n\r\n[data-theme=\"dark\"] .btn-group .btn.active {\r\n  background: var(--primary-color);\r\n  border-color: var(--primary-color);\r\n  color: white;\r\n}\r\n\r\n[data-theme=\"dark\"] .btn-group .btn:hover:not(.active) {\r\n  background: var(--hover-bg-color);\r\n  border-color: var(--primary-color);\r\n  color: var(--primary-color);\r\n}\r\n\r\n"], "mappings": ";AAEA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;AAIA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,cAAc,EAAE;AAAA,MAAE,KAAK,IAAI,WAAW,EAAE,KAAK;AACvF;AACA,CAHC,uBAGuB,CAAC;AACvB;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,QAAQ,EAAE;AAAA,MAAE,QAAQ;AAC9D;AACA,CANC,uBAMuB,CAAC;AACvB,SAAO,IAAI;AACb;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,KAAK,IAAI,cAAc,EAAE,KAAK;AAC7F;AACA,CAHC,yBAGyB,CAXD;AAYvB;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,gBAAgB;AAC7E;AACA,CANC,yBAMyB,CAXD;AAYvB,SAAO,IAAI;AACb;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,KAAK,IAAI,cAAc,EAAE,KAAK;AAC3F,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,IAAI,cAAc,EAAE;AAClD;AACA,CALC,0BAK0B,CAxBF;AAyBvB;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,QAAQ;AACjE;AACA,CARC,0BAQ0B,CAxBF;AAyBvB,SAAO,IAAI;AACX,eAAa;AACf;AACA,CAZC,0BAY0B,CAAC;AAC1B,eAAa;AACf;AACA,CAfC,0BAe0B;AACzB,eAAa;AACf;AAGA,CAAC;AACC,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,gBAAgB;AAAA,MAAE,IAAI;AAC9D,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,aAAW;AACb;AAGA,CAAC;AACC,YAAU;AACZ;AAEA,CAJC,iBAIiB,CAAC;AACjB,gBAAc;AACd,iBAAe;AACf,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,cAAY,IAAI,KAAK;AACvB;AAEA,CAbC,iBAaiB,CATC,YASY;AAC7B,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,OAAO,KAAK,IAAI,gBAAgB,EAAE;AACpD,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,YAAU;AACV,QAAM;AACN,OAAK;AACL,aAAW,WAAW;AACtB,SAAO,IAAI;AACX,WAAS;AACX;AAGA,CAAC;AACC,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,gBAAgB;AAAA,MAAE,IAAI;AAC9D,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,aAAW;AACX,eAAa;AACf;AAGA,CAAC;AACC,iBAAe;AACf,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,cAAY,IAAI,KAAK;AACvB;AAEA,CAPC,WAOW;AACV,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrC,aAAW,WAAW;AACxB;AAGA,CAbC,YAaY,CAAC;AACZ,WAAS;AACX;AAEA,CAjBC,YAiBY,CAJC,mBAImB,CAAC;AAChC,UAAQ;AACR,SAAO;AACT;AAEA,CAtBC,YAsBY,CATC,mBASmB,CALC,IAKI,EAAE;AACtC,WAAS,EAAE;AACb;AAGA,CA3BC,YA2BY;AACX,SAAO,IAAI;AACX,iBAAe;AACjB;AAEA,CAhCC,YAgCY,CAAC;AACZ,SAAO,IAAI;AACb;AAGA,CArCC,YAqCY,CAAC;AACZ,SAAO,IAAI;AACX,eAAa;AACf;AAGA,CA3CC,YA2CY,CAAC;AACZ,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,eAAa;AACb,cAAY,IAAI,KAAK;AACvB;AAEA,CAlDC,YAkDY,CAPC,WAOW;AACvB,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,IAAI,gBAAgB,EAAE;AACpD;AAGA,CAAC;AACC,WAAS,KAAK;AAChB;AAEA,CAJC,YAIY;AACX,WAAS;AACX;AAGA,CAAC;AACC,iBAAe;AACjB;AAGA,CAAC,UAAU,CAAC;AACV,iBAAe;AACf,gBAAc,IAAI;AAClB,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,cAAY,IAAI,KAAK;AACvB;AAEA,CARC,UAQU,CARC,GAQG;AACb,0BAAwB;AACxB,6BAA2B;AAC7B;AAEA,CAbC,UAaU,CAbC,GAaG;AACb,2BAAyB;AACzB,8BAA4B;AAC9B;AAEA,CAlBC,UAkBU,CAlBC,GAkBG,CAAC;AACd,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,SAAO;AACP,cAAY,EAAE,IAAI,IAAI,KAAK,IAAI,gBAAgB,EAAE;AACnD;AAEA,CAzBC,UAyBU,CAzBC,GAyBG,MAAM,KAAK,CAPV;AAQd,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CA/BC,UA+BU,CA/BC,IA+BI,CAAC;AACf,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO;AACT;AAEA,CApCC,UAoCU,CApCC,GAoCG,KAAK,CAlBJ,QAkBa,CALZ;AAMf,cAAY,IAAI;AAChB,SAAO;AACT;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA3KD;AA4KG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GAvID;AAwIG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GAAC;AACC,mBAAe;AACjB;AAEA,GA3DD;AA4DG,oBAAgB;AAClB;AAEA,GA/DD,UA+DY,CA/DD;AAgER,mBAAe;AACf,mBAAe;AACjB;AAEA,GApED,UAoEY,CApED,GAoEK;AACb,mBAAe;AACjB;AAGA,GAjJD,YAiJc,CApID,mBAoIqB,CAhID;AAiI9B,YAAQ;AACV;AAEA,GArJD,YAqJc,CAxID,mBAwIqB,CApID,IAoIM,EAAE;AACtC,aAAS,OAAO;AAClB;AAGA,GA1JD,YA0Jc,CA7ID,mBA6IqB,CAAC,QAAQ,CAAC;AACzC,aAAS;AACX;AAGA,GA/JD,YA+Jc,CAlJD,mBAkJqB,CALC,QAKQ,CAAC,QAAQ,CAAC;AAClD,gBAAY;AACd;AAEA,GAnKD,YAmKc,CAtJD,mBAsJqB,CATC,QASQ,CAJC,QAIQ,CAJC,OAIO,CAAC;AAC1D,qBAAiB;AACnB;AACF;AAGA,OAAO,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,SAAS,EAAE;AACxC,GA1KD,YA0Kc,CA7JD,mBA6JqB,CAhBC,QAgBQ,CAhBC;AAiBzC,gBAAY;AACd;AAEA,GA9KD,YA8Kc,CAjKD,mBAiKqB,CApBC,QAoBQ,CApBC,SAoBS,CAXQ;AAY1D,qBAAiB;AACnB;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GArDC,kBAqDkB,CAhRI;AAiRrB,eAAW;AACb;AAEA,GAzDC,kBAyDkB,CAxPO;AAyPxB,eAAW;AACb;AAEA,GAAC;AACC,eAAW;AACb;AAEA,GAJC,aAIa;AAAA,EACd,CALC,aAKa;AACZ,aAAS,OAAO;AAClB;AAEA,GAAC;AACC,eAAW;AACX,aAAS,QAAQ;AACnB;AAGA,GAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE;AAChC,aAAS;AACX;AAEA,GAJC,MAIM;AACL,aAAS;AACX;AACF;AAGA,CAAC,iBAAmB,CAhQnB,iBAgQqC,CA5PnB;AA6PjB,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAtQnB,iBAsQqC,CAlQnB,YAkQgC;AACjD,gBAAc,IAAI;AAClB,cAAY,IAAI;AAClB;AAEA,CAAC,iBAAmB,CAhOnB;AAiOC,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC,iBAAmB,CA9JnB,UA8J8B,CA9JnB;AA+JV,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CApKnB,UAoK8B,CApKnB,GAoKuB,CAlJnB;AAmJd,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,SAAO;AACT;AAEA,CAAC,iBAAmB,CA1KnB,UA0K8B,CA1KnB,GA0KuB,MAAM,KAAK,CAxJ9B;AAyJd,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;", "names": []}