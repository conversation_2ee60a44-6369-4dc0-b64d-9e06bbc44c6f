{"version": 3, "sources": ["src/app/components/user-licenses-list/user-licenses-list.component.css"], "sourcesContent": ["/* User Licenses List Component Styles - Modern Design System */\r\n\r\n/* Filter Card Styles */\r\n.filter-card {\r\n  margin-bottom: 1.5rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.filter-card:hover {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.filter-section {\r\n  margin-bottom: 1.5rem;\r\n  padding-bottom: 1.5rem;\r\n  border-bottom: 1px solid var(--border-color);\r\n}\r\n\r\n.filter-section:last-child {\r\n  border-bottom: none;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.filter-title {\r\n  font-weight: 600;\r\n  color: var(--primary);\r\n  margin-bottom: 1rem;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* Modern Form Elements */\r\n.modern-label {\r\n  font-weight: 500;\r\n  color: var(--text-primary);\r\n  margin-bottom: 0.5rem;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.modern-input {\r\n  width: 100%;\r\n  padding: 0.75rem 1rem;\r\n  border: 1px solid var(--border-color);\r\n  border-radius: var(--border-radius-md);\r\n  background-color: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: 0.875rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.modern-input:focus {\r\n  outline: none;\r\n  border-color: var(--primary);\r\n  box-shadow: 0 0 0 3px var(--primary-light);\r\n}\r\n\r\n.modern-input-group {\r\n  position: relative;\r\n}\r\n\r\n.modern-input-icon {\r\n  position: absolute;\r\n  left: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: var(--text-secondary);\r\n  z-index: 2;\r\n}\r\n\r\n.modern-input-group .modern-input {\r\n  padding-left: 2.5rem;\r\n}\r\n\r\n.modern-select {\r\n  width: 100%;\r\n  padding: 0.75rem 1rem;\r\n  border: 1px solid var(--border-color);\r\n  border-radius: var(--border-radius-md);\r\n  background-color: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: 0.875rem;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.modern-select:focus {\r\n  outline: none;\r\n  border-color: var(--primary);\r\n  box-shadow: 0 0 0 3px var(--primary-light);\r\n}\r\n\r\n.modern-select-sm {\r\n  padding: 0.5rem 0.75rem;\r\n  font-size: 0.8125rem;\r\n}\r\n\r\n/* Table Container */\r\n.table-container {\r\n  overflow-x: auto;\r\n  margin-bottom: 1.5rem;\r\n  border-radius: var(--border-radius-md);\r\n  border: 1px solid var(--border-color);\r\n}\r\n\r\n/* Table Row Hover Effects */\r\n.modern-table tbody tr {\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.modern-table tbody tr:hover {\r\n  background-color: var(--primary-light);\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* Company Info with Avatar */\r\n.company-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n}\r\n\r\n/* User Info */\r\n.user-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n}\r\n\r\n/* Date Info */\r\n.date-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.25rem;\r\n}\r\n\r\n/* Remaining Days Styling */\r\n.remaining-days {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-weight: 500;\r\n}\r\n\r\n/* Action Buttons */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n/* Modern Button Hover Effects */\r\n.modern-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.modern-btn:active {\r\n  transform: translateY(0);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.modern-btn-sm {\r\n  padding: 0.375rem 0.75rem;\r\n  font-size: 0.8125rem;\r\n  min-width: 2.5rem;\r\n  height: 2.5rem;\r\n}\r\n\r\n/* Button Loading State */\r\n.modern-btn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  transform: none !important;\r\n  box-shadow: none !important;\r\n}\r\n\r\n/* Modern Avatar */\r\n.modern-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* Modern Badges */\r\n.modern-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  padding: 0.375rem 0.75rem;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n  border-radius: var(--border-radius-pill);\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.modern-badge-primary {\r\n  background-color: var(--primary-light);\r\n  color: var(--primary);\r\n  border: 1px solid var(--primary);\r\n}\r\n\r\n.modern-badge-secondary {\r\n  background-color: var(--secondary-light);\r\n  color: var(--secondary);\r\n  border: 1px solid var(--secondary);\r\n}\r\n\r\n.modern-badge-success {\r\n  background-color: var(--success-light);\r\n  color: var(--success);\r\n  border: 1px solid var(--success);\r\n}\r\n\r\n.modern-badge-warning {\r\n  background-color: var(--warning-light);\r\n  color: var(--warning);\r\n  border: 1px solid var(--warning);\r\n}\r\n\r\n.modern-badge-danger {\r\n  background-color: var(--danger-light);\r\n  color: var(--danger);\r\n  border: 1px solid var(--danger);\r\n}\r\n\r\n.modern-badge-info {\r\n  background-color: var(--info-light);\r\n  color: var(--info);\r\n  border: 1px solid var(--info);\r\n}\r\n\r\n/* Empty State */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 3rem 1rem;\r\n}\r\n\r\n.empty-state-icon {\r\n  font-size: 4rem;\r\n  color: var(--text-secondary);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.empty-state-title {\r\n  color: var(--text-secondary);\r\n  margin-bottom: 0.5rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.empty-state-text {\r\n  color: var(--text-secondary);\r\n  margin-bottom: 0;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* Modern Pagination */\r\n.pagination-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.modern-pagination {\r\n  display: flex;\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.modern-page-item {\r\n  display: flex;\r\n}\r\n\r\n.modern-page-link {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0.5rem 0.75rem;\r\n  min-width: 2.5rem;\r\n  height: 2.5rem;\r\n  border: 1px solid var(--border-color);\r\n  background-color: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  text-decoration: none;\r\n  border-radius: var(--border-radius-sm);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.modern-page-link:hover:not(:disabled) {\r\n  background-color: var(--primary-light);\r\n  border-color: var(--primary);\r\n  color: var(--primary);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.modern-page-item.active .modern-page-link {\r\n  background-color: var(--primary);\r\n  border-color: var(--primary);\r\n  color: white;\r\n}\r\n\r\n.modern-page-item.disabled .modern-page-link {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n  pointer-events: none;\r\n}\r\n\r\n.pagination-info {\r\n  text-align: center;\r\n}\r\n\r\n.pagination-info small {\r\n  color: var(--text-secondary);\r\n  font-size: 0.8125rem;\r\n}\r\n\r\n/* Animation Classes */\r\n.zoom-in {\r\n  animation: zoomIn 0.3s ease-out;\r\n}\r\n\r\n@keyframes zoomIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.95);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .container-fluid {\r\n    padding-left: 1rem;\r\n    padding-right: 1rem;\r\n  }\r\n\r\n  .modern-card-header {\r\n    flex-direction: column;\r\n    align-items: flex-start !important;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .modern-card-header .modern-btn {\r\n    width: 100%;\r\n  }\r\n\r\n  .table-container {\r\n    font-size: 0.875rem;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 0.25rem;\r\n  }\r\n\r\n  .modern-btn-sm {\r\n    padding: 0.375rem 0.5rem;\r\n    font-size: 0.75rem;\r\n  }\r\n\r\n  .company-info,\r\n  .user-info {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 0.25rem;\r\n  }\r\n\r\n  .modern-avatar {\r\n    width: 32px;\r\n    height: 32px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .modern-pagination {\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n  }\r\n\r\n  .modern-page-link {\r\n    min-width: 2rem;\r\n    height: 2rem;\r\n    padding: 0.25rem 0.5rem;\r\n    font-size: 0.75rem;\r\n  }\r\n}\r\n\r\n/* Dark Mode Support - Automatic Detection */\r\n@media (prefers-color-scheme: dark) {\r\n  .modern-input,\r\n  .modern-select {\r\n    background-color: var(--bg-secondary);\r\n    border-color: var(--border-color);\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  .modern-input:focus,\r\n  .modern-select:focus {\r\n    border-color: var(--primary);\r\n    box-shadow: 0 0 0 3px var(--primary-light);\r\n  }\r\n\r\n  .modern-input-icon {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  .empty-state-icon {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  .empty-state-title,\r\n  .empty-state-text {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  .modern-page-link {\r\n    background-color: var(--bg-secondary);\r\n    border-color: var(--border-color);\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  .modern-page-link:hover:not(:disabled) {\r\n    background-color: var(--primary-light);\r\n    border-color: var(--primary);\r\n    color: var(--primary);\r\n  }\r\n\r\n  .modern-page-item.active .modern-page-link {\r\n    background-color: var(--primary);\r\n    border-color: var(--primary);\r\n    color: white;\r\n  }\r\n}\r\n\r\n/* Dark Mode Support - Manual Theme Toggle */\r\n[data-theme=\"dark\"] {\r\n  .modern-input,\r\n  .modern-select {\r\n    background-color: var(--bg-secondary);\r\n    border-color: var(--border-color);\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  .modern-input:focus,\r\n  .modern-select:focus {\r\n    border-color: var(--primary);\r\n    box-shadow: 0 0 0 3px var(--primary-light);\r\n  }\r\n\r\n  .modern-input-icon {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  .empty-state-icon {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  .empty-state-title,\r\n  .empty-state-text {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  .modern-page-link {\r\n    background-color: var(--bg-secondary);\r\n    border-color: var(--border-color);\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  .modern-page-link:hover:not(:disabled) {\r\n    background-color: var(--primary-light);\r\n    border-color: var(--primary);\r\n    color: var(--primary);\r\n  }\r\n\r\n  .modern-page-item.active .modern-page-link {\r\n    background-color: var(--primary);\r\n    border-color: var(--primary);\r\n    color: white;\r\n  }\r\n}"], "mappings": ";AAGA,CAAC;AACC,iBAAe;AACf,cAAY,IAAI,KAAK;AACvB;AAEA,CALC,WAKW;AACV,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,iBAAe;AACf,kBAAgB;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CANC,cAMc;AACb,iBAAe;AACf,kBAAgB;AAClB;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACT,eAAa;AACf;AAGA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACT,eAAa;AACb,aAAW;AACb;AAEA,CAAC;AACC,SAAO;AACP,WAAS,QAAQ;AACjB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,aAAW;AACX,cAAY,IAAI,KAAK;AACvB;AAEA,CAXC,YAWY;AACX,WAAS;AACT,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,IAAI;AAC5B;AAEA,CAAC;AACC,YAAU;AACZ;AAEA,CAAC;AACC,YAAU;AACV,QAAM;AACN,OAAK;AACL,aAAW,WAAW;AACtB,SAAO,IAAI;AACX,WAAS;AACX;AAEA,CAbC,mBAamB,CA9BnB;AA+BC,gBAAc;AAChB;AAEA,CAAC;AACC,SAAO;AACP,WAAS,QAAQ;AACjB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,aAAW;AACX,cAAY,IAAI,KAAK;AACrB,UAAQ;AACV;AAEA,CAZC,aAYa;AACZ,WAAS;AACT,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,IAAI;AAC5B;AAEA,CAAC;AACC,WAAS,OAAO;AAChB,aAAW;AACb;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,iBAAe,IAAI;AACnB,UAAQ,IAAI,MAAM,IAAI;AACxB;AAGA,CAAC,aAAa,MAAM;AAClB,cAAY,IAAI,KAAK;AACrB,UAAQ;AACV;AAEA,CALC,aAKa,MAAM,EAAE;AACpB,oBAAkB,IAAI;AACtB,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,eAAa;AACf;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,OAAK;AACP;AAGA,CAAC,UAAU;AACT,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CALC,UAKU;AACT,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC;AACC,WAAS,SAAS;AAClB,aAAW;AACX,aAAW;AACX,UAAQ;AACV;AAGA,CAlBC,UAkBU;AACT,WAAS;AACT,UAAQ;AACR,aAAW;AACX,cAAY;AACd;AAGA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,eAAa;AACb,aAAW;AACX,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,eAAa;AACf;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,SAAS;AAClB,aAAW;AACX,eAAa;AACb,iBAAe,IAAI;AACnB,kBAAgB;AAChB,kBAAgB;AAChB,cAAY,IAAI,KAAK;AACvB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAGA,CAAC;AACC,cAAY;AACZ,WAAS,KAAK;AAChB;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,iBAAe;AACjB;AAEA,CAAC;AACC,SAAO,IAAI;AACX,iBAAe;AACf,eAAa;AACf;AAEA,CAAC;AACC,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACX;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,cAAY;AACZ,WAAS;AACT,UAAQ;AACR,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS,OAAO;AAChB,aAAW;AACX,UAAQ;AACR,UAAQ,IAAI,MAAM,IAAI;AACtB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,mBAAiB;AACjB,iBAAe,IAAI;AACnB,cAAY,IAAI,KAAK;AACrB,UAAQ;AACR,aAAW;AACX,eAAa;AACf;AAEA,CAlBC,gBAkBgB,MAAM,KAAK;AAC1B,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACX,aAAW,WAAW;AACxB;AAEA,CA7BC,gBA6BgB,CAAC,OAAO,CAzBxB;AA0BC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO;AACT;AAEA,CAnCC,gBAmCgB,CAAC,SAAS,CA/B1B;AAgCC,WAAS;AACT,UAAQ;AACR,kBAAgB;AAClB;AAEA,CAAC;AACC,cAAY;AACd;AAEA,CAJC,gBAIgB;AACf,SAAO,IAAI;AACX,aAAW;AACb;AAGA,CAAC;AACC,aAAW,OAAO,KAAK;AACzB;AAEA,WAHa;AAIX;AACE,aAAS;AACT,eAAW,MAAM;AACnB;AACA;AACE,aAAS;AACT,eAAW,MAAM;AACnB;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,kBAAc;AACd,mBAAe;AACjB;AAEA,GAAC;AACC,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GANC,mBAMmB,CAhNrB;AAiNG,WAAO;AACT;AAEA,GA5QD;AA6QG,eAAW;AACb;AAEA,GA/ND;AAgOG,oBAAgB;AAChB,SAAK;AACP;AAEA,GAnND;AAoNG,aAAS,SAAS;AAClB,eAAW;AACb;AAEA,GAtQD;AAAA,EAuQC,CAhQD;AAiQG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GA/MD;AAgNG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GAtHD;AAuHG,eAAW;AACX,qBAAiB;AACnB;AAEA,GA/GD;AAgHG,eAAW;AACX,YAAQ;AACR,aAAS,QAAQ;AACjB,eAAW;AACb;AACF;AAGA,OAAO,CAAC,oBAAoB,EAAE;AAC5B,GAhXD;AAAA,EAiXC,CA/UD;AAgVG,sBAAkB,IAAI;AACtB,kBAAc,IAAI;AAClB,WAAO,IAAI;AACb;AAEA,GAvXD,YAuXc;AAAA,EACb,CAtVD,aAsVe;AACZ,kBAAc,IAAI;AAClB,gBAAY,EAAE,EAAE,EAAE,IAAI,IAAI;AAC5B;AAEA,GAxWD;AAyWG,WAAO,IAAI;AACb;AAEA,GAhLD;AAiLG,WAAO,IAAI;AACb;AAEA,GA9KD;AAAA,EA+KC,CAzKD;AA0KG,WAAO,IAAI;AACb;AAEA,GAnJD;AAoJG,sBAAkB,IAAI;AACtB,kBAAc,IAAI;AAClB,WAAO,IAAI;AACb;AAEA,GAzJD,gBAyJkB,MAAM,KAAK;AAC1B,sBAAkB,IAAI;AACtB,kBAAc,IAAI;AAClB,WAAO,IAAI;AACb;AAEA,GAnKD,gBAmKkB,CAtID,OAsIS,CA/J1B;AAgKG,sBAAkB,IAAI;AACtB,kBAAc,IAAI;AAClB,WAAO;AACT;AACF;AAGA,CAAC,iBACC,CA/ZD;AA8ZD,CAAC,iBAEC,CA9XD;AA+XG,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AANF,CAAC,iBAQC,CAtaD,YAsac;AARf,CAAC,iBASC,CArYD,aAqYe;AACZ,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,IAAI;AAC5B;AAZF,CAAC,iBAcC,CAvZD;AAwZG,SAAO,IAAI;AACb;AAhBF,CAAC,iBAkBC,CA/ND;AAgOG,SAAO,IAAI;AACb;AApBF,CAAC,iBAsBC,CA7ND;AAuMD,CAAC,iBAuBC,CAxND;AAyNG,SAAO,IAAI;AACb;AAzBF,CAAC,iBA2BC,CAlMD;AAmMG,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AA/BF,CAAC,iBAiCC,CAxMD,gBAwMkB,MAAM,KAAK;AAC1B,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AArCF,CAAC,iBAuCC,CAlND,gBAkNkB,CArLD,OAqLS,CA9M1B;AA+MG,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO;AACT;", "names": []}