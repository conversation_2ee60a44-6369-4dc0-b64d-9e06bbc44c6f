{"version": 3, "sources": ["src/app/components/extend-license/extend-license.component.css"], "sourcesContent": ["/* Extend License Dialog - Simplified Modern Design */\r\n\r\n/* Dialog Container */\r\n.extend-license-dialog {\r\n  width: 100%;\r\n  max-width: 500px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* Simplified User Info */\r\n.user-info-simple {\r\n  padding: 1rem;\r\n  background-color: var(--bg-secondary);\r\n  border-radius: var(--border-radius-md);\r\n  border: 1px solid var(--border-color);\r\n}\r\n\r\n/* Modern Radio Group */\r\n.modern-radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.modern-radio {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 0.75rem;\r\n  padding: 1rem;\r\n  border: 2px solid var(--border-color);\r\n  border-radius: var(--border-radius-md);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  background-color: var(--bg-primary);\r\n}\r\n\r\n.modern-radio:hover {\r\n  border-color: var(--primary);\r\n  background-color: var(--primary-light);\r\n}\r\n\r\n.modern-radio input[type=\"radio\"] {\r\n  display: none;\r\n}\r\n\r\n.modern-radio input[type=\"radio\"]:checked + .radio-mark {\r\n  background-color: var(--primary);\r\n  border-color: var(--primary);\r\n}\r\n\r\n.modern-radio input[type=\"radio\"]:checked + .radio-mark::after {\r\n  opacity: 1;\r\n}\r\n\r\n.modern-radio input[type=\"radio\"]:checked ~ .radio-content strong {\r\n  color: var(--primary);\r\n}\r\n\r\n.radio-mark {\r\n  width: 20px;\r\n  height: 20px;\r\n  border: 2px solid var(--border-color);\r\n  border-radius: 50%;\r\n  background-color: var(--bg-primary);\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n  flex-shrink: 0;\r\n  margin-top: 2px;\r\n}\r\n\r\n.radio-mark::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: white;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.radio-content {\r\n  flex: 1;\r\n}\r\n\r\n.radio-content strong {\r\n  display: block;\r\n  margin-bottom: 0.25rem;\r\n  color: var(--text-primary);\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n/* Package Selection - Simple Select Style */\r\n.select-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.select-wrapper .input-icon {\r\n  position: absolute;\r\n  left: 1rem;\r\n  color: var(--text-secondary);\r\n  z-index: 2;\r\n  pointer-events: none;\r\n}\r\n\r\n.modern-select {\r\n  width: 100%;\r\n  padding: 0.75rem 1rem 0.75rem 2.5rem;\r\n  border: 2px solid var(--border-color);\r\n  border-radius: var(--border-radius-md);\r\n  background-color: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: 0.875rem;\r\n  transition: all 0.3s ease;\r\n  appearance: none;\r\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\");\r\n  background-position: right 0.75rem center;\r\n  background-repeat: no-repeat;\r\n  background-size: 1.5em 1.5em;\r\n}\r\n\r\n.modern-select:focus {\r\n  outline: none;\r\n  border-color: var(--primary-color);\r\n  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);\r\n}\r\n\r\n/* Package Info */\r\n.package-info {\r\n  margin-top: 1rem;\r\n}\r\n\r\n.info-card {\r\n  background-color: var(--bg-secondary);\r\n  border: 1px solid var(--border-color);\r\n  border-radius: var(--border-radius-md);\r\n  padding: 1rem;\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.info-header h6 {\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  margin: 0;\r\n}\r\n\r\n.price-badge {\r\n  background-color: var(--primary-color);\r\n  color: white;\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: var(--border-radius-sm);\r\n  font-weight: 600;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.info-details {\r\n  display: flex;\r\n  gap: 1.5rem;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-size: 0.875rem;\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.description {\r\n  margin: 0;\r\n  font-size: 0.875rem;\r\n  color: var(--text-secondary);\r\n  line-height: 1.4;\r\n}\r\n\r\n.empty-state-small {\r\n  text-align: center;\r\n  padding: 2rem;\r\n  color: var(--text-secondary);\r\n}\r\n\r\n/* Modern Input Group */\r\n.modern-input-group {\r\n  display: flex;\r\n  align-items: stretch;\r\n  border-radius: var(--border-radius-md);\r\n  overflow: hidden;\r\n  border: 2px solid var(--border-color);\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.modern-input-group:focus-within {\r\n  border-color: var(--primary);\r\n  box-shadow: 0 0 0 3px var(--primary-light);\r\n}\r\n\r\n.modern-input-group .modern-input {\r\n  border: none;\r\n  border-radius: 0;\r\n  flex: 1;\r\n}\r\n\r\n.modern-input-group .modern-input:focus {\r\n  box-shadow: none;\r\n}\r\n\r\n.modern-input-suffix {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0.75rem 1rem;\r\n  background-color: var(--bg-secondary);\r\n  color: var(--text-secondary);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  border-left: 1px solid var(--border-color);\r\n}\r\n\r\n/* Modern Label */\r\n.modern-label {\r\n  display: block;\r\n  font-weight: 500;\r\n  color: var(--text-primary);\r\n  margin-bottom: 0.5rem;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n/* Form Error */\r\n.form-error {\r\n  display: flex;\r\n  align-items: center;\r\n  color: var(--danger);\r\n  font-size: 0.8125rem;\r\n  margin-top: 0.5rem;\r\n}\r\n\r\n/* Form Section */\r\n.form-section {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .extend-license-dialog {\r\n    max-width: 95vw;\r\n    margin: 1rem;\r\n  }\r\n\r\n  .user-info-simple .d-flex {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .modern-radio {\r\n    padding: 0.75rem;\r\n  }\r\n\r\n  .info-details {\r\n    flex-direction: column;\r\n    gap: 0.75rem;\r\n  }\r\n}\r\n\r\n/* Payment Methods */\r\n.payment-methods {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\r\n  gap: 0.75rem;\r\n  margin-top: 0.5rem;\r\n}\r\n\r\n.payment-method-option {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 1rem 0.75rem;\r\n  border: 2px solid var(--border-color);\r\n  border-radius: var(--border-radius-md);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  background-color: var(--bg-primary);\r\n  text-align: center;\r\n}\r\n\r\n.payment-method-option:hover {\r\n  border-color: var(--primary);\r\n  background-color: var(--primary-light);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.payment-method-option.selected {\r\n  border-color: var(--primary);\r\n  background-color: var(--primary-light);\r\n  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);\r\n}\r\n\r\n.payment-method-icon {\r\n  font-size: 1.5rem;\r\n  color: var(--text-secondary);\r\n  margin-bottom: 0.5rem;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.payment-method-option:hover .payment-method-icon,\r\n.payment-method-option.selected .payment-method-icon {\r\n  color: var(--primary);\r\n}\r\n\r\n.payment-method-label {\r\n  font-size: 0.8125rem;\r\n  font-weight: 500;\r\n  color: var(--text-primary);\r\n  line-height: 1.2;\r\n}\r\n\r\n/* Animation */\r\n.extend-license-dialog {\r\n  animation: slideInUp 0.3s ease-out;\r\n}\r\n\r\n@keyframes slideInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";AAGA,CAAC;AACC,SAAO;AACP,aAAW;AACX,UAAQ,EAAE;AACZ;AAGA,CAAC;AACC,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,UAAQ,IAAI,MAAM,IAAI;AACxB;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS;AACT,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,oBAAkB,IAAI;AACxB;AAEA,CAZC,YAYY;AACX,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACxB;AAEA,CAjBC,aAiBa,KAAK,CAAC;AAClB,WAAS;AACX;AAEA,CArBC,aAqBa,KAAK,CAAC,WAAa,SAAS,EAAE,CAAC;AAC3C,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CA1BC,aA0Ba,KAAK,CAAC,WAAa,SAAS,EAAE,CALC,UAKU;AACrD,WAAS;AACX;AAEA,CA9BC,aA8Ba,KAAK,CAAC,WAAa,SAAS,EAAE,CAAC,cAAc;AACzD,SAAO,IAAI;AACb;AAEA,CAb6C;AAc3C,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe;AACf,oBAAkB,IAAI;AACtB,YAAU;AACV,cAAY,IAAI,KAAK;AACrB,eAAa;AACb,cAAY;AACd;AAEA,CAzB6C,UAyBlC;AACT,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,aAAW,UAAU,IAAI,EAAE;AAC3B,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,oBAAkB;AAClB,WAAS;AACT,cAAY,QAAQ,KAAK;AAC3B;AAEA,CA9B6C;AA+B3C,QAAM;AACR;AAEA,CAlC6C,cAkC9B;AACb,WAAS;AACT,iBAAe;AACf,SAAO,IAAI;AACX,cAAY,MAAM,KAAK;AACzB;AAGA,CAAC;AACC,YAAU;AACV,WAAS;AACT,eAAa;AACf;AAEA,CANC,eAMe,CAAC;AACf,YAAU;AACV,QAAM;AACN,SAAO,IAAI;AACX,WAAS;AACT,kBAAgB;AAClB;AAEA,CAAC;AACC,SAAO;AACP,WAAS,QAAQ,KAAK,QAAQ;AAC9B,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,aAAW;AACX,cAAY,IAAI,KAAK;AACrB,cAAY;AACZ,oBAAkB;AAClB,uBAAqB,MAAM,QAAQ;AACnC,qBAAmB;AACnB,mBAAiB,MAAM;AACzB;AAEA,CAhBC,aAgBa;AACZ,WAAS;AACT,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,IAAI,cAAc,EAAE;AACjD;AAGA,CAAC;AACC,cAAY;AACd;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe;AACjB;AAEA,CAPC,YAOY;AACX,SAAO,IAAI;AACX,eAAa;AACb,UAAQ;AACV;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO;AACP,WAAS,QAAQ;AACjB,iBAAe,IAAI;AACnB,eAAa;AACb,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,UAAQ;AACR,aAAW;AACX,SAAO,IAAI;AACX,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,SAAO,IAAI;AACb;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,iBAAe,IAAI;AACnB,YAAU;AACV,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY,aAAa,KAAK;AAChC;AAEA,CATC,kBASkB;AACjB,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,IAAI;AAC5B;AAEA,CAdC,mBAcmB,CAAC;AACnB,UAAQ;AACR,iBAAe;AACf,QAAM;AACR;AAEA,CApBC,mBAoBmB,CANC,YAMY;AAC/B,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,QAAQ;AACjB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,aAAW;AACX,eAAa;AACb,eAAa,IAAI,MAAM,IAAI;AAC7B;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACf,aAAW;AACb;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,SAAO,IAAI;AACX,aAAW;AACX,cAAY;AACd;AAGA,CAAC;AACC,iBAAe;AACjB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA3PD;AA4PG,eAAW;AACX,YAAQ;AACV;AAEA,GAzPD,iBAyPmB,CAAC;AACjB,oBAAgB;AAChB,gBAAY;AACZ,SAAK;AACP;AAEA,GAjPD;AAkPG,aAAS;AACX;AAEA,GAvGD;AAwGG,oBAAgB;AAChB,SAAK;AACP;AACF;AAGA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACL,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,WAAS,KAAK;AACd,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,oBAAkB,IAAI;AACtB,cAAY;AACd;AAEA,CAbC,qBAaqB;AACpB,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACtB,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CApBC,qBAoBqB,CAAC;AACrB,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACtB,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,IAAI,cAAc,EAAE;AACjD;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,iBAAe;AACf,cAAY,MAAM,KAAK;AACzB;AAEA,CAjCC,qBAiCqB,OAAO,CAP5B;AAQD,CAlCC,qBAkCqB,CAdC,SAcS,CAR/B;AASC,SAAO,IAAI;AACb;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACX,eAAa;AACf;AAGA,CAtUC;AAuUC,aAAW,UAAU,KAAK;AAC5B;AAEA,WAHa;AAIX;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACA;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACF;", "names": []}