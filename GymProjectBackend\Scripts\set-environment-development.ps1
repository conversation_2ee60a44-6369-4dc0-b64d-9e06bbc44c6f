# Development Environment Setup Script
# Bu script Development environment için gerekli environment variables'ları set eder

Write-Host "=== GymKod Development Environment Setup ===" -ForegroundColor Green
Write-Host "Setting up Development environment variables..." -ForegroundColor Yellow

# ASPNETCORE Environment
$env:ASPNETCORE_ENVIRONMENT = "Development"
[System.Environment]::SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Development", "User")

# Connection String (Environment Variable olarak da set edebilirsin)
$connectionString = "Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False"
$env:ConnectionStrings__DefaultConnection = $connectionString
[System.Environment]::SetEnvironmentVariable("ConnectionStrings__DefaultConnection", $connectionString, "User")

# Security Key (Production'da farklı olmalı)
$securityKey = "zX9c2Kf8Lm3Qp7Rt5Vw1Hy4Njt6Bd0Gs3Ae8Iu2Zy5Tx7Fq1Cm9Pk4Wv6Hn2Jl8Rd0Se3Gb7Mt"
$env:TokenOptions__SecurityKey = $securityKey
[System.Environment]::SetEnvironmentVariable("TokenOptions__SecurityKey", $securityKey, "User")

Write-Host "✅ Development environment variables set successfully!" -ForegroundColor Green
Write-Host "Current ASPNETCORE_ENVIRONMENT: $env:ASPNETCORE_ENVIRONMENT" -ForegroundColor Cyan
Write-Host "Connection String configured for: GymProject database" -ForegroundColor Cyan

Write-Host "`n=== Next Steps ===" -ForegroundColor Yellow
Write-Host "1. Restart your IDE/Terminal to pick up new environment variables" -ForegroundColor White
Write-Host "2. Run: dotnet run --project WebAPI" -ForegroundColor White
Write-Host "3. Application will automatically use Development configuration" -ForegroundColor White

pause
