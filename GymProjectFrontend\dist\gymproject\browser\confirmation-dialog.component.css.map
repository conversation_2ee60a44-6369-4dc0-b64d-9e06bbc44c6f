{"version": 3, "sources": ["angular:styles/component:css;49f26b6854100a9fe02810985ff6ff0535dc2ccc582a5151aa87aa08e04429a7;C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectFrontend\\src\\app\\components\\confirmation-dialog\\confirmation-dialog.component.html"], "sourcesContent": ["\n  .modern-dialog {\n    min-width: 320px;\n    max-width: 100%;\n    overflow: hidden;\n    border-radius: var(--border-radius-lg);\n    box-shadow: var(--shadow-md);\n    background-color: var(--bg-primary);\n    color: var(--text-primary);\n  }\n\n  .modern-card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: var(--spacing-md) var(--spacing-lg);\n    border-bottom: 1px solid var(--border-color);\n    background-color: var(--bg-secondary);\n    color: var(--text-primary);\n  }\n\n  .confirmation-dialog.delete-type .modern-card-header {\n    background-color: var(--danger-light);\n    color: var(--text-primary);\n  }\n\n  .confirmation-dialog.info-type .modern-card-header {\n    background-color: var(--info-light);\n    color: var(--text-primary);\n  }\n\n  .confirmation-dialog.warning-type .modern-card-header {\n    background-color: var(--warning-light);\n    color: var(--text-primary);\n  }\n\n  .confirmation-dialog.success-type .modern-card-header {\n    background-color: var(--success-light);\n    color: var(--text-primary);\n  }\n\n  .modern-card-header h2 {\n    margin: 0;\n    font-size: 1.25rem;\n    font-weight: 600;\n    color: var(--text-primary);\n  }\n\n  .modern-card-body {\n    padding: var(--spacing-lg);\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    text-align: center;\n  }\n\n  .dialog-icon {\n    width: 64px;\n    height: 64px;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: var(--spacing-md);\n    font-size: 1.75rem;\n  }\n\n  .dialog-icon i {\n    color: white;\n  }\n\n  .dialog-icon.warning {\n    background-color: var(--warning);\n  }\n\n  .dialog-icon.danger {\n    background-color: var(--danger);\n  }\n\n  .dialog-icon.info {\n    background-color: var(--info);\n  }\n\n  .dialog-icon.success {\n    background-color: var(--success);\n  }\n\n  .dialog-message {\n    font-size: 1rem;\n    margin: 0 0 var(--spacing-md);\n    line-height: 1.5;\n    max-width: 400px;\n    color: var(--text-primary);\n    white-space: pre-line;\n  }\n\n  .detailed-warning {\n    text-align: left !important;\n    max-width: 100%;\n  }\n\n  .detailed-warning pre {\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    font-family: inherit;\n    font-size: 0.95rem;\n    line-height: 1.6;\n    margin: 0;\n    color: var(--text-primary);\n    background: none;\n    border: none;\n    padding: 0;\n  }\n\n  .modern-card-footer {\n    padding: var(--spacing-md) var(--spacing-lg);\n    border-top: 1px solid var(--border-color);\n    background-color: var(--bg-secondary);\n    display: flex;\n    justify-content: flex-end;\n    gap: var(--spacing-sm);\n  }\n\n  .modern-btn-icon {\n    width: 32px;\n    height: 32px;\n    border-radius: 50%;\n    border: none;\n    background: transparent;\n    color: var(--text-primary);\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: all 0.3s ease;\n    font-size: 1rem;\n  }\n\n  .modern-btn-icon:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n    color: var(--text-primary);\n  }\n\n  /* Dark mode specific styles */\n  [data-theme=\"dark\"] .confirmation-dialog {\n    background-color: var(--bg-secondary);\n    color: var(--text-primary);\n  }\n\n  [data-theme=\"dark\"] .modern-card-header {\n    background-color: var(--bg-tertiary);\n    color: var(--text-primary);\n  }\n\n  [data-theme=\"dark\"] .confirmation-dialog.delete-type .modern-card-header {\n    background-color: rgba(244, 67, 54, 0.2);\n    color: var(--text-primary);\n  }\n\n  [data-theme=\"dark\"] .confirmation-dialog.info-type .modern-card-header {\n    background-color: rgba(100, 181, 246, 0.2);\n    color: var(--text-primary);\n  }\n\n  [data-theme=\"dark\"] .confirmation-dialog.warning-type .modern-card-header {\n    background-color: rgba(255, 193, 7, 0.2);\n    color: var(--text-primary);\n  }\n\n  [data-theme=\"dark\"] .confirmation-dialog.success-type .modern-card-header {\n    background-color: rgba(76, 175, 80, 0.2);\n    color: var(--text-primary);\n  }\n\n  [data-theme=\"dark\"] .dialog-message,\n  [data-theme=\"dark\"] .detailed-warning pre {\n    color: var(--text-primary);\n  }\n\n  [data-theme=\"dark\"] .modern-card-footer {\n    background-color: var(--bg-tertiary);\n  }\n\n  [data-theme=\"dark\"] .modern-btn-icon {\n    color: var(--text-primary);\n  }\n\n  [data-theme=\"dark\"] .modern-btn-icon:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n    color: var(--text-primary);\n  }\n\n  /* Responsive Design */\n  @media screen and (max-width: 768px) {\n    .modern-dialog {\n      min-width: 280px;\n      max-width: 95vw;\n    }\n\n    .modern-card-header {\n      padding: var(--spacing-sm) var(--spacing-md);\n    }\n\n    .modern-card-body {\n      padding: var(--spacing-md);\n    }\n\n    .modern-card-footer {\n      padding: var(--spacing-sm) var(--spacing-md);\n      flex-direction: column;\n    }\n\n    .modern-card-footer .modern-btn {\n      width: 100%;\n      margin-bottom: var(--spacing-xs);\n    }\n\n    .modern-card-footer .modern-btn:last-child {\n      margin-bottom: 0;\n    }\n\n    .dialog-icon {\n      width: 48px;\n      height: 48px;\n      font-size: 1.25rem;\n    }\n  }\n\n  @media screen and (max-width: 480px) {\n    .modern-dialog {\n      min-width: 260px;\n    }\n\n    .dialog-message {\n      font-size: 0.875rem;\n    }\n  }\n"], "mappings": ";AACE,CAAC;AACC,aAAW;AACX,aAAW;AACX,YAAU;AACV,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS,IAAI,cAAc,IAAI;AAC/B,iBAAe,IAAI,MAAM,IAAI;AAC7B,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC,mBAAmB,CAAC,YAAY,CAVhC;AAWC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CALC,mBAKmB,CAAC,UAAU,CAf9B;AAgBC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAVC,mBAUmB,CAAC,aAAa,CApBjC;AAqBC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAfC,mBAemB,CAAC,aAAa,CAzBjC;AA0BC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CA9BC,mBA8BmB;AAClB,UAAQ;AACR,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS,IAAI;AACb,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,cAAY;AACd;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,iBAAe,IAAI;AACnB,aAAW;AACb;AAEA,CAXC,YAWY;AACX,SAAO;AACT;AAEA,CAfC,WAeW,CAAC;AACX,oBAAkB,IAAI;AACxB;AAEA,CAnBC,WAmBW,CAAC;AACX,oBAAkB,IAAI;AACxB;AAEA,CAvBC,WAuBW,CAAC;AACX,oBAAkB,IAAI;AACxB;AAEA,CA3BC,WA2BW,CAAC;AACX,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,aAAW;AACX,UAAQ,EAAE,EAAE,IAAI;AAChB,eAAa;AACb,aAAW;AACX,SAAO,IAAI;AACX,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACZ,aAAW;AACb;AAEA,CALC,iBAKiB;AAChB,eAAa;AACb,aAAW;AACX,eAAa;AACb,aAAW;AACX,eAAa;AACb,UAAQ;AACR,SAAO,IAAI;AACX,cAAY;AACZ,UAAQ;AACR,WAAS;AACX;AAEA,CAAC;AACC,WAAS,IAAI,cAAc,IAAI;AAC/B,cAAY,IAAI,MAAM,IAAI;AAC1B,oBAAkB,IAAI;AACtB,WAAS;AACT,mBAAiB;AACjB,OAAK,IAAI;AACX;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,UAAQ;AACR,cAAY;AACZ,SAAO,IAAI;AACX,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,cAAY,IAAI,KAAK;AACrB,aAAW;AACb;AAEA,CAfC,eAee;AACd,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,SAAO,IAAI;AACb;AAGA,CAAC,iBAAmB,CA3HnB;AA4HC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA1InB;AA2IC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CArInB,mBAqIuC,CArInB,YAqIgC,CA/IpD;AAgJC,oBAAkB,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACpC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA1InB,mBA0IuC,CArInB,UAqI8B,CApJlD;AAqJC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA/InB,mBA+IuC,CArInB,aAqIiC,CAzJrD;AA0JC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE;AACpC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CApJnB,mBAoJuC,CArInB,aAqIiC,CA9JrD;AA+JC,oBAAkB,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;AACpC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAvFnB;AAwFD,CAAC,iBAAmB,CA/EnB,iBA+EqC;AACpC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAjEnB;AAkEC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CA5DnB;AA6DC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAhEnB,eAgEmC;AAClC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,SAAO,IAAI;AACb;AAGA,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE;AAC5B,GAjMD;AAkMG,eAAW;AACX,eAAW;AACb;AAEA,GA5LD;AA6LG,aAAS,IAAI,cAAc,IAAI;AACjC;AAEA,GA3JD;AA4JG,aAAS,IAAI;AACf;AAEA,GA7FD;AA8FG,aAAS,IAAI,cAAc,IAAI;AAC/B,oBAAgB;AAClB;AAEA,GAlGD,mBAkGqB,CAAC;AACnB,WAAO;AACP,mBAAe,IAAI;AACrB;AAEA,GAvGD,mBAuGqB,CALC,UAKU;AAC7B,mBAAe;AACjB;AAEA,GArKD;AAsKG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AACF;AAEA,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE;AAC5B,GApOD;AAqOG,eAAW;AACb;AAEA,GAlJD;AAmJG,eAAW;AACb;AACF;", "names": []}