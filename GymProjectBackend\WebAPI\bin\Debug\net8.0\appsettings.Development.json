{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False"}, "Environment": {"Name": "Development", "AllowedOrigins": ["http://localhost:4200", "http://localhost:3000", "http://*************:4200"], "EnableSwagger": true, "EnableDetailedErrors": true, "UseHttpsRedirection": false}, "TokenOptions": {"Audience": "https://api.gymkod.com", "Issuer": "https://admin.gymkod.com", "AccessTokenExpiration": 15, "RefreshTokenExpiration": 30, "SecurityKey": "zX9c2Kf8Lm3Qp7Rt5Vw1Hy4Njt6Bd0Gs3Ae8Iu2Zy5Tx7Fq1Cm9Pk4Wv6Hn2Jl8Rd0Se3Gb7Mt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}}