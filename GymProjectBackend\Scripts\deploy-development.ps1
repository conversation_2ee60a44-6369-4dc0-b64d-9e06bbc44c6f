# Development Deployment Script
# Bu script Development environment için tam otomatik deployment yapar

param(
    [switch]$SkipBuild = $false,
    [switch]$SkipFrontend = $false
)

Write-Host "=== GymKod Development Deployment ===" -ForegroundColor Green
Write-Host "Starting Development deployment process..." -ForegroundColor Yellow

# Set Environment
Write-Host "`n1. Setting Environment Variables..." -ForegroundColor Cyan
$env:ASPNETCORE_ENVIRONMENT = "Development"
$env:ConnectionStrings__DefaultConnection = "Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False"

Write-Host "✅ Environment set to: Development" -ForegroundColor Green

# Backend Build
if (-not $SkipBuild) {
    Write-Host "`n2. Building Backend..." -ForegroundColor Cyan
    Set-Location "GymProjectBackend"
    
    Write-Host "Restoring packages..." -ForegroundColor Yellow
    dotnet restore
    
    Write-Host "Building solution..." -ForegroundColor Yellow
    dotnet build --configuration Debug
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Backend build failed!" -ForegroundColor Red
        pause
        exit 1
    }
    
    Write-Host "✅ Backend build successful!" -ForegroundColor Green
    Set-Location ".."
}

# Frontend Build
if (-not $SkipFrontend) {
    Write-Host "`n3. Building Frontend..." -ForegroundColor Cyan
    Set-Location "GymProjectFrontend"
    
    Write-Host "Installing npm packages..." -ForegroundColor Yellow
    npm install
    
    Write-Host "Building Angular app for Development..." -ForegroundColor Yellow
    ng build --configuration=development
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Frontend build failed!" -ForegroundColor Red
        pause
        exit 1
    }
    
    Write-Host "✅ Frontend build successful!" -ForegroundColor Green
    Set-Location ".."
}

Write-Host "`n4. Starting Application..." -ForegroundColor Cyan
Write-Host "Backend will start on: http://localhost:5165" -ForegroundColor Yellow
Write-Host "Frontend will be available on: http://localhost:4200" -ForegroundColor Yellow

Write-Host "`n=== Development Deployment Complete! ===" -ForegroundColor Green
Write-Host "✅ Environment: Development" -ForegroundColor Green
Write-Host "✅ Database: GymProject (Local)" -ForegroundColor Green
Write-Host "✅ API URL: http://localhost:5165/api/" -ForegroundColor Green

Write-Host "`nTo start the application:" -ForegroundColor Yellow
Write-Host "Backend: cd GymProjectBackend && dotnet run --project WebAPI" -ForegroundColor White
Write-Host "Frontend: cd GymProjectFrontend && ng serve" -ForegroundColor White

pause
