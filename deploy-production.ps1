# Production Deployment Script - Ana Klasörden Çalıştır
# Bu script Production environment için tam otomatik deployment yapar

param(
    [switch]$SkipBuild = $false,
    [switch]$SkipFrontend = $false,
    [switch]$Force = $false
)

Write-Host "=== GymKod Production Deployment ===" -ForegroundColor Red
Write-Host "⚠️  WARNING: PRODUCTION DEPLOYMENT!" -ForegroundColor Red
Write-Host "This will deploy to LIVE environment with 1000+ gym users!" -ForegroundColor Red
Write-Host "Target: admin.gymkod.com" -ForegroundColor Red

if (-not $Force) {
    $confirmation = Read-Host "`nAre you absolutely sure? Type 'DEPLOY-PRODUCTION' to confirm"
    if ($confirmation -ne "DEPLOY-PRODUCTION") {
        Write-Host "❌ Production deployment cancelled." -ForegroundColor Yellow
        pause
        exit
    }
}

Write-Host "`nStarting Production deployment process..." -ForegroundColor Yellow

# Set Environment
Write-Host "`n1. Setting Environment Variables..." -ForegroundColor Cyan
$env:ASPNETCORE_ENVIRONMENT = "Production"
$env:ConnectionStrings__DefaultConnection = "Server=localhost;User Id=sa;Password=************;Database=GymProject;Trusted_Connection=false;Encrypt=False"

Write-Host "✅ Environment set to: Production" -ForegroundColor Green

# Backend Build
if (-not $SkipBuild) {
    Write-Host "`n2. Building Backend..." -ForegroundColor Cyan
    Set-Location "GymProjectBackend"
    
    Write-Host "Restoring packages..." -ForegroundColor Yellow
    dotnet restore
    
    Write-Host "Building solution for Production..." -ForegroundColor Yellow
    dotnet build --configuration Release
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Backend build failed!" -ForegroundColor Red
        Set-Location ".."
        pause
        exit 1
    }
    
    Write-Host "✅ Backend build successful!" -ForegroundColor Green
    Set-Location ".."
}

# Frontend Build
if (-not $SkipFrontend) {
    Write-Host "`n3. Building Frontend..." -ForegroundColor Cyan
    Set-Location "GymProjectFrontend"
    
    Write-Host "Installing npm packages..." -ForegroundColor Yellow
    npm install
    
    Write-Host "Building Angular app for Production..." -ForegroundColor Yellow
    ng build --configuration=production
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Frontend build failed!" -ForegroundColor Red
        Set-Location ".."
        pause
        exit 1
    }
    
    Write-Host "✅ Frontend build successful!" -ForegroundColor Green
    Set-Location ".."
}

Write-Host "`n4. Production Deployment Ready..." -ForegroundColor Cyan
Write-Host "Files are ready for deployment to admin.gymkod.com" -ForegroundColor Yellow

Write-Host "`n=== Production Deployment Complete! ===" -ForegroundColor Green
Write-Host "✅ Environment: Production" -ForegroundColor Red
Write-Host "✅ Database: GymProject (LIVE)" -ForegroundColor Red
Write-Host "✅ API URL: https://admin.gymkod.com/api/" -ForegroundColor Green
Write-Host "✅ Frontend URL: https://admin.gymkod.com" -ForegroundColor Green

Write-Host "`n⚠️  CRITICAL NEXT STEPS:" -ForegroundColor Red
Write-Host "1. BACKUP production database before deployment!" -ForegroundColor Red
Write-Host "2. Copy backend files to production server" -ForegroundColor White
Write-Host "3. Copy frontend dist files to production web directory" -ForegroundColor White
Write-Host "4. Restart production server" -ForegroundColor White
Write-Host "5. Test thoroughly on admin.gymkod.com" -ForegroundColor White
Write-Host "6. Monitor logs for any issues" -ForegroundColor White

pause
