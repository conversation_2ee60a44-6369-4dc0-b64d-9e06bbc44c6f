# GymKod Environment Configuration Guide

B<PERSON> kılavuz, GymKod projesinin yeni environment-based configuration sistemini kullanmanızı sağlar.

## 🎯 Artık Ne Değişti?

### ❌ Eskiden (<PERSON>):
1. GymContext.cs'yi aç → Connection string'i değiştir
2. Program.cs'yi aç → CORS ayarlarını değiştir  
3. Frontend'te BaseApiService'i aç → API URL'ini değiştir
4. Build al → Deploy et

### ✅ Şimdi (Otomatik):
1. Environment script'ini çalıştır
2. Deploy script'ini çalıştır
3. **SONUÇ:** Otomatik olarak doğru DB, doğru CORS, doğru API URL!

## 🚀 Hızlı Başlangıç

### Development Environment
```powershell
# Environment'ı set et
.\Scripts\set-environment-development.ps1

# Deploy et
.\Scripts\deploy-development.ps1

# Veya sadece çalıştır
cd GymProjectBackend && dotnet run --project WebAPI
cd GymProjectFrontend && ng serve
```

### Staging Environment
```powershell
# Environment'ı set et
.\Scripts\set-environment-staging.ps1

# Deploy et
.\Scripts\deploy-staging.ps1

# Test et: staging.gymkod.com
```

### Production Environment
```powershell
# Environment'ı set et (DİKKATLİ!)
.\Scripts\set-environment-production.ps1

# Deploy et (DİKKATLİ!)
.\Scripts\deploy-production.ps1

# Live: admin.gymkod.com
```

## 📁 Environment Dosyaları

### Backend (appsettings)
- `appsettings.Development.json` → Local development
- `appsettings.Staging.json` → staging.gymkod.com
- `appsettings.Production.json` → admin.gymkod.com

### Frontend (Angular environments)
- `environment.development.ts` → http://localhost:5165/api/
- `environment.staging.ts` → https://staging.gymkod.com/api/
- `environment.production.ts` → https://admin.gymkod.com/api/

## 🔧 Manuel Kullanım

### Environment Variable'ları Manuel Set Etme
```powershell
# Development
$env:ASPNETCORE_ENVIRONMENT = "Development"

# Staging
$env:ASPNETCORE_ENVIRONMENT = "Staging"

# Production
$env:ASPNETCORE_ENVIRONMENT = "Production"
```

### Frontend Build Commands
```bash
# Development
ng build --configuration=development
ng serve --configuration=development

# Staging
ng build --configuration=staging
ng serve --configuration=staging

# Production
ng build --configuration=production
```

### Backend Run Commands
```bash
# Otomatik environment detection
dotnet run --project WebAPI

# Specific environment
dotnet run --project WebAPI --environment=Development
dotnet run --project WebAPI --environment=Staging
dotnet run --project WebAPI --environment=Production
```

## 🛡️ Güvenlik Notları

1. **Production'da farklı SecurityKey kullan!**
2. **Environment variables'ları güvenli tut**
3. **Production deployment'tan önce backup al**
4. **Staging'de test et, sonra Production'a geç**

## 🔍 Troubleshooting

### Problem: Environment değişmiyor
**Çözüm:** IDE/Terminal'i restart et

### Problem: Connection string çalışmıyor
**Çözüm:** Environment variable'ın doğru set edildiğini kontrol et
```powershell
echo $env:ASPNETCORE_ENVIRONMENT
echo $env:ConnectionStrings__DefaultConnection
```

### Problem: Frontend yanlış API'ye bağlanıyor
**Çözüm:** Doğru configuration ile build et
```bash
ng build --configuration=staging
```

## 📞 Destek

Herhangi bir sorun yaşarsan:
1. Environment variable'ları kontrol et
2. Script'leri tekrar çalıştır
3. IDE'yi restart et
4. Log'ları kontrol et

**Artık hiç kod değiştirmene gerek yok! 🎉**
