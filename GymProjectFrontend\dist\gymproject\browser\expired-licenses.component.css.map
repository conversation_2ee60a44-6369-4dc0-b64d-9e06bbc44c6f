{"version": 3, "sources": ["src/app/components/expired-licenses/expired-licenses.component.css"], "sourcesContent": ["/* Expired Licenses Component - Modern Design */\n\n/* Empty State */\n.empty-state {\n  text-align: center;\n  padding: 3rem 2rem;\n}\n\n.empty-state-icon {\n  font-size: 4rem;\n  margin-bottom: 1.5rem;\n  opacity: 0.7;\n}\n\n.empty-state-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n  color: var(--text-primary);\n}\n\n.empty-state-description {\n  color: var(--text-secondary);\n  margin-bottom: 2rem;\n  font-size: 1rem;\n}\n\n/* Modern Spinner */\n.modern-spinner {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 2rem;\n}\n\n.spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid var(--border-color);\n  border-top: 4px solid var(--primary);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Modern Badge Styles */\n.modern-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  border-radius: 50rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border: 1px solid transparent;\n  transition: all 0.3s ease;\n}\n\n.modern-badge-warning {\n  background-color: var(--warning-light);\n  color: var(--warning);\n  border-color: rgba(var(--warning-rgb), 0.3);\n}\n\n.total-members-badge {\n  display: flex;\n  align-items: center;\n}\n\n/* Modern Card Styles */\n.modern-card {\n  border-radius: 0.75rem;\n  border: none;\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  background-color: var(--card-bg-color);\n}\n\n.modern-card:hover {\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\n}\n\n.modern-card .card-header {\n  background-color: transparent;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\n  padding: 1rem 1.5rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.modern-card .card-body {\n  padding: 1.5rem;\n}\n\n/* Company Info Styling */\n.company-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.company-name {\n  font-weight: 600;\n  color: var(--text-primary);\n  font-size: 0.9rem;\n}\n\n.company-id {\n  font-size: 0.75rem;\n  opacity: 0.7;\n}\n\n/* Search Input Styling */\n.search-input {\n  transition: all 0.3s ease;\n}\n\n.search-help-text {\n  display: block;\n  margin-top: 0.5rem;\n  color: var(--text-muted);\n  font-size: 0.75rem;\n  font-style: italic;\n}\n\n/* Modern Input Styles */\n.modern-label {\n  display: block;\n  font-weight: 500;\n  color: var(--text-primary);\n  margin-bottom: 0.5rem;\n  font-size: 0.875rem;\n}\n\n.modern-input {\n  width: 100%;\n  padding: 0.75rem 1rem;\n  border: 2px solid var(--border-color);\n  border-radius: var(--border-radius-md);\n  background-color: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  transition: all 0.3s ease;\n}\n\n.modern-input:focus {\n  outline: none;\n  border-color: var(--primary);\n  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);\n}\n\n.modern-input::placeholder {\n  color: var(--text-secondary);\n  opacity: 0.7;\n}\n\n.modern-select {\n  width: 100%;\n  padding: 0.75rem 1rem;\n  border: 2px solid var(--border-color);\n  border-radius: var(--border-radius-md);\n  background-color: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.modern-select:focus {\n  outline: none;\n  border-color: var(--primary);\n  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);\n}\n\n/* Modern Table Styles */\n.modern-table-container {\n  overflow-x: auto;\n  border-radius: var(--border-radius-md);\n}\n\n.modern-table {\n  width: 100%;\n  border-collapse: separate;\n  border-spacing: 0;\n  margin-bottom: 1.5rem;\n}\n\n.modern-table th {\n  font-weight: 600;\n  text-transform: uppercase;\n  font-size: 0.75rem;\n  letter-spacing: 0.5px;\n  padding: 1rem;\n  background-color: rgba(0, 0, 0, 0.02);\n  border-bottom: 1px solid var(--border-color);\n  color: var(--text-muted);\n}\n\n.modern-table td {\n  padding: 1rem;\n  vertical-align: middle;\n  border-bottom: 1px solid var(--border-color);\n}\n\n.modern-table tbody tr {\n  transition: all 0.2s ease;\n}\n\n.modern-table tbody tr:hover {\n  background-color: var(--bg-secondary);\n}\n\n/* Modern Badge Styles */\n.modern-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.375rem 0.75rem;\n  font-size: 0.75rem;\n  font-weight: 500;\n  border-radius: var(--border-radius-sm);\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n\n.modern-badge-info {\n  background-color: var(--info-light);\n  color: var(--info-dark);\n  border: 1px solid var(--info);\n}\n\n.modern-badge-secondary {\n  background-color: var(--secondary-light);\n  color: var(--secondary-dark);\n  border: 1px solid var(--secondary);\n}\n\n.modern-badge-danger {\n  background-color: var(--danger-light);\n  color: var(--danger-dark);\n  border: 1px solid var(--danger);\n}\n\n.modern-badge-warning {\n  background-color: var(--warning-light);\n  color: var(--warning-dark);\n  border: 1px solid var(--warning);\n}\n\n.modern-badge-success {\n  background-color: var(--success-light);\n  color: var(--success-dark);\n  border: 1px solid var(--success);\n}\n\n/* Modern Pagination */\n.modern-pagination {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.modern-pagination-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: 2px solid var(--border-color);\n  border-radius: var(--border-radius-md);\n  background-color: var(--bg-primary);\n  color: var(--text-primary);\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.modern-pagination-btn:hover:not(.disabled) {\n  border-color: var(--primary);\n  background-color: var(--primary-light);\n  color: var(--primary);\n}\n\n.modern-pagination-btn.active {\n  border-color: var(--primary);\n  background-color: var(--primary);\n  color: white;\n}\n\n.modern-pagination-btn.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n\n.modern-pagination-info {\n  text-align: center;\n  margin-top: 1rem;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container-fluid {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .modern-card-header {\n    flex-direction: column;\n    align-items: flex-start !important;\n    gap: 1rem;\n  }\n\n  .modern-card-header .modern-btn {\n    width: 100%;\n  }\n\n  /* Filter form responsive */\n  .row.g-3.align-items-end {\n    align-items: stretch !important;\n  }\n\n  .row.g-3.align-items-end .col-md-4 {\n    margin-top: 1rem;\n  }\n\n  .modern-table-container {\n    font-size: 0.875rem;\n  }\n\n  .modern-table thead th,\n  .modern-table tbody td {\n    padding: 0.75rem 0.5rem;\n  }\n\n  .modern-btn-sm {\n    padding: 0.25rem 0.5rem;\n    font-size: 0.75rem;\n  }\n\n  .modern-pagination {\n    flex-wrap: wrap;\n    gap: 0.25rem;\n  }\n\n  .modern-pagination-btn {\n    width: 35px;\n    height: 35px;\n    font-size: 0.875rem;\n  }\n\n  .empty-state {\n    padding: 2rem 1rem;\n  }\n\n  .empty-state-icon {\n    font-size: 3rem;\n  }\n\n  .empty-state-title {\n    font-size: 1.25rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .modern-card-body {\n    padding: 1rem;\n  }\n\n  .modern-input,\n  .modern-select {\n    padding: 0.625rem 0.75rem;\n    font-size: 0.875rem;\n  }\n\n  .modern-table-container {\n    font-size: 0.8125rem;\n  }\n\n  .modern-table thead th,\n  .modern-table tbody td {\n    padding: 0.5rem 0.375rem;\n  }\n\n  .modern-btn-sm {\n    padding: 0.125rem 0.25rem;\n    font-size: 0.7rem;\n  }\n\n  .modern-badge {\n    padding: 0.25rem 0.5rem;\n    font-size: 0.6875rem;\n  }\n}\n\n\n"], "mappings": ";AAGA,CAAC;AACC,cAAY;AACZ,WAAS,KAAK;AAChB;AAEA,CAAC;AACC,aAAW;AACX,iBAAe;AACf,WAAS;AACX;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,iBAAe;AACf,SAAO,IAAI;AACb;AAEA,CAAC;AACC,SAAO,IAAI;AACX,iBAAe;AACf,aAAW;AACb;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACX;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY,IAAI,MAAM,IAAI;AAC1B,iBAAe;AACf,aAAW,KAAK,GAAG,OAAO;AAC5B;AAEA,WAHa;AAIX;AAAK,eAAW,OAAO;AAAO;AAC9B;AAAO,eAAW,OAAO;AAAS;AACpC;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,OAAO;AAChB,iBAAe;AACf,aAAW;AACX,eAAa;AACb,UAAQ,IAAI,MAAM;AAClB,cAAY,IAAI,KAAK;AACvB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,gBAAc,KAAK,IAAI,cAAc,EAAE;AACzC;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACf;AAGA,CAAC;AACC,iBAAe;AACf,UAAQ;AACR,cAAY,EAAE,SAAS,QAAQ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC7C,cAAY,IAAI,KAAK;AACrB,YAAU;AACV,oBAAkB,IAAI;AACxB;AAEA,CATC,WASW;AACV,cAAY,EAAE,OAAO,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC1C;AAEA,CAbC,YAaY,CAAC;AACZ,oBAAkB;AAClB,iBAAe,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC,WAAS,KAAK;AACd,WAAS;AACT,mBAAiB;AACjB,eAAa;AACf;AAEA,CAtBC,YAsBY,CAAC;AACZ,WAAS;AACX;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,WAAS;AACX;AAGA,CAAC;AACC,cAAY,IAAI,KAAK;AACvB;AAEA,CAAC;AACC,WAAS;AACT,cAAY;AACZ,SAAO,IAAI;AACX,aAAW;AACX,cAAY;AACd;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACf,aAAW;AACb;AAEA,CAAC;AACC,SAAO;AACP,WAAS,QAAQ;AACjB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,aAAW;AACX,cAAY,IAAI,KAAK;AACvB;AAEA,CAXC,YAWY;AACX,WAAS;AACT,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,IAAI,cAAc,EAAE;AACjD;AAEA,CAjBC,YAiBY;AACX,SAAO,IAAI;AACX,WAAS;AACX;AAEA,CAAC;AACC,SAAO;AACP,WAAS,QAAQ;AACjB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,aAAW;AACX,UAAQ;AACR,cAAY,IAAI,KAAK;AACvB;AAEA,CAZC,aAYa;AACZ,WAAS;AACT,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,IAAI,cAAc,EAAE;AACjD;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,SAAO;AACP,mBAAiB;AACjB,kBAAgB;AAChB,iBAAe;AACjB;AAEA,CAPC,aAOa;AACZ,eAAa;AACb,kBAAgB;AAChB,aAAW;AACX,kBAAgB;AAChB,WAAS;AACT,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,iBAAe,IAAI,MAAM,IAAI;AAC7B,SAAO,IAAI;AACb;AAEA,CAlBC,aAkBa;AACZ,WAAS;AACT,kBAAgB;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAxBC,aAwBa,MAAM;AAClB,cAAY,IAAI,KAAK;AACvB;AAEA,CA5BC,aA4Ba,MAAM,EAAE;AACpB,oBAAkB,IAAI;AACxB;AAGA,CAxKC;AAyKC,WAAS;AACT,eAAa;AACb,WAAS,SAAS;AAClB,aAAW;AACX,eAAa;AACb,iBAAe,IAAI;AACnB,kBAAgB;AAChB,kBAAgB;AAClB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CA1LC;AA2LC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,OAAK;AACL,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,eAAa;AACb,UAAQ;AACR,cAAY,IAAI,KAAK;AACvB;AAEA,CAfC,qBAeqB,MAAM,KAAK,CAAC;AAChC,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CArBC,qBAqBqB,CAAC;AACrB,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACtB,SAAO;AACT;AAEA,CA3BC,qBA2BqB,CAZY;AAahC,WAAS;AACT,UAAQ;AACR,kBAAgB;AAClB;AAEA,CAAC;AACC,cAAY;AACZ,cAAY;AACd;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,kBAAc;AACd,mBAAe;AACjB;AAEA,GAAC;AACC,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GANC,mBAMmB,CAAC;AACnB,WAAO;AACT;AAGA,GAAC,GAAG,CAAC,GAAG,CAAC;AACP,iBAAa;AACf;AAEA,GAJC,GAIG,CAJC,GAIG,CAJC,gBAIgB,CAAC;AACxB,gBAAY;AACd;AAEA,GAxJD;AAyJG,eAAW;AACb;AAEA,GAvJD,aAuJe,MAAM;AAAA,EACpB,CAxJD,aAwJe,MAAM;AAClB,aAAS,QAAQ;AACnB;AAEA,GAAC;AACC,aAAS,QAAQ;AACjB,eAAW;AACb;AAEA,GAtFD;AAuFG,eAAW;AACX,SAAK;AACP;AAEA,GAnFD;AAoFG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GAlWD;AAmWG,aAAS,KAAK;AAChB;AAEA,GAjWD;AAkWG,eAAW;AACb;AAEA,GA/VD;AAgWG,eAAW;AACb;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,aAAS;AACX;AAEA,GA5OD;AAAA,EA6OC,CAvND;AAwNG,aAAS,SAAS;AAClB,eAAW;AACb;AAEA,GAzMD;AA0MG,eAAW;AACb;AAEA,GAxMD,aAwMe,MAAM;AAAA,EACpB,CAzMD,aAyMe,MAAM;AAClB,aAAS,OAAO;AAClB;AAEA,GAjDC;AAkDC,aAAS,SAAS;AAClB,eAAW;AACb;AAEA,GAzVD;AA0VG,aAAS,QAAQ;AACjB,eAAW;AACb;AACF;", "names": []}