{"version": 3, "sources": ["src/app/components/member-delete-dialog/member-delete-dialog.component.css"], "sourcesContent": ["/* Main Container */\n.delete-dialog-container {\n  width: 100%;\n  max-width: 800px;\n  background-color: var(--bg-primary);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-lg);\n  color: var(--text-primary);\n  font-family: inherit;\n  animation: dialogFadeIn 0.3s ease-out;\n  display: flex;\n  flex-direction: column;\n  max-height: 80vh;\n}\n\n/* Animation */\n@keyframes dialogFadeIn {\n  from {\n    opacity: 0;\n    transform: scale(0.9) translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n/* Header */\n.dialog-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 1.5rem;\n  background: linear-gradient(135deg, var(--danger), #c82333);\n  color: white;\n  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  flex-shrink: 0;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.header-icon {\n  width: 32px;\n  height: 32px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1rem;\n}\n\n.dialog-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n}\n\n.close-btn {\n  width: 32px;\n  height: 32px;\n  background: rgba(255, 255, 255, 0.1);\n  border: none;\n  border-radius: 50%;\n  color: white;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background-color 0.2s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n/* Content */\n.dialog-content {\n  padding: 1.5rem;\n  flex: 1;\n  overflow-y: auto;\n  min-height: 0;\n}\n\n/* Custom Scrollbar - Using CSS Variables */\n.dialog-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.dialog-content::-webkit-scrollbar-track {\n  background: var(--bg-secondary);\n  border-radius: 3px;\n}\n\n.dialog-content::-webkit-scrollbar-thumb {\n  background: var(--border-color);\n  border-radius: 3px;\n}\n\n.dialog-content::-webkit-scrollbar-thumb:hover {\n  background: var(--text-secondary);\n}\n\n/* Member Section */\n.member-section {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n  padding: 1rem;\n  background-color: var(--bg-secondary);\n  border-radius: var(--border-radius-md);\n  border-left: 4px solid var(--danger);\n}\n\n.member-avatar {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 1.125rem;\n  flex-shrink: 0;\n}\n\n.member-details {\n  flex: 1;\n}\n\n.member-name {\n  margin: 0 0 0.25rem 0;\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--text-primary);\n}\n\n.member-subtitle {\n  margin: 0;\n  font-size: 0.875rem;\n  color: var(--text-secondary);\n}\n\n/* Warning Section */\n.warning-section {\n  margin-bottom: 1.5rem;\n}\n\n.warning-box {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  background-color: var(--warning-light);\n  border: 1px solid var(--warning);\n  border-radius: var(--border-radius-md);\n  color: var(--warning-dark);\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.warning-box i {\n  font-size: 1.125rem;\n  flex-shrink: 0;\n}\n\n/* Selection Section */\n.selection-section {\n  margin-bottom: 1rem;\n}\n\n.selection-section h4 {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 600;\n  color: var(--text-primary);\n}\n\n/* Memberships Container */\n.memberships-container {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n/* Membership Card */\n.membership-card {\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n  padding: 1rem;\n  border: 2px solid var(--border-color);\n  border-radius: var(--border-radius-md);\n  background-color: var(--bg-secondary);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.membership-card:hover {\n  border-color: var(--danger);\n  box-shadow: 0 2px 8px rgba(var(--danger-rgb), 0.1);\n}\n\n.membership-card.selected {\n  border-color: var(--danger);\n  background-color: var(--danger-light);\n  box-shadow: 0 2px 12px rgba(var(--danger-rgb), 0.15);\n}\n\n.membership-radio {\n  margin-top: 0.125rem;\n}\n\n.membership-radio input[type=\"radio\"] {\n  width: 18px;\n  height: 18px;\n  accent-color: var(--danger);\n}\n\n.membership-content {\n  flex: 1;\n  min-width: 0;\n}\n\n/* Membership Header */\n.membership-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 0.75rem;\n  gap: 1rem;\n}\n\n.package-name {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  line-height: 1.2;\n  flex: 1;\n}\n\n.branch-name {\n  font-size: 0.875rem;\n  color: var(--text-secondary);\n  background-color: var(--bg-tertiary);\n  padding: 0.375rem 0.75rem;\n  border-radius: var(--border-radius-sm);\n  white-space: nowrap;\n  font-weight: 500;\n}\n\n/* Membership Meta */\n.membership-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n}\n\n.remaining-time,\n.date-range {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.875rem;\n}\n\n.remaining-time {\n  font-weight: 600;\n}\n\n.remaining-time i,\n.date-range i {\n  width: 14px;\n  text-align: center;\n  opacity: 0.7;\n}\n\n/* Status Colors - Using CSS Variables */\n.remaining-time.status-good {\n  color: var(--success);\n}\n\n.remaining-time.status-warning {\n  color: var(--warning);\n}\n\n.remaining-time.status-danger {\n  color: var(--danger);\n}\n\n.date-range {\n  color: var(--text-secondary);\n}\n\n/* Membership Status */\n.membership-status {\n  margin-top: 0.5rem;\n}\n\n.frozen-indicator {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  background: linear-gradient(135deg, #74b9ff, #0984e3);\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: var(--border-radius-sm);\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n/* Footer */\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.75rem;\n  padding: 1rem 1.5rem;\n  border-top: 1px solid var(--border-color);\n  background-color: var(--bg-secondary);\n  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);\n  position: sticky;\n  bottom: 0;\n  z-index: 10;\n  flex-shrink: 0;\n}\n\n/* Buttons */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: var(--border-radius-md);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-decoration: none;\n  min-width: 120px;\n  justify-content: center;\n}\n\n.btn i {\n  font-size: 0.875rem;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.btn-cancel {\n  background-color: transparent;\n  color: var(--text-secondary);\n  border: 1px solid var(--border-color);\n}\n\n.btn-cancel:hover:not(:disabled) {\n  background-color: var(--bg-tertiary);\n  border-color: var(--text-secondary);\n}\n\n.btn-delete {\n  background-color: var(--danger);\n  color: white;\n}\n\n.btn-delete:hover:not(:disabled) {\n  background-color: var(--danger-dark);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(var(--danger-rgb), 0.3);\n}\n\n/* Default Light Mode Styles - Using CSS Variables */\n.delete-dialog-container {\n  background-color: var(--bg-primary);\n  color: var(--text-primary);\n}\n\n.member-section {\n  background-color: var(--bg-secondary);\n  color: var(--text-primary);\n}\n\n.member-name {\n  color: var(--text-primary);\n}\n\n.member-subtitle {\n  color: var(--text-secondary);\n}\n\n.warning-box {\n  background-color: var(--warning-light);\n  border-color: var(--warning);\n  color: var(--warning);\n}\n\n.selection-section h4 {\n  color: var(--text-primary);\n}\n\n.membership-card {\n  background-color: var(--bg-secondary);\n  border-color: var(--border-color);\n  color: var(--text-primary);\n}\n\n.membership-card.selected {\n  background-color: var(--danger-light);\n  border-color: var(--danger);\n}\n\n.package-name {\n  color: var(--text-primary);\n}\n\n.branch-name {\n  background-color: var(--bg-tertiary);\n  color: var(--text-secondary);\n}\n\n.remaining-time {\n  color: var(--text-primary);\n}\n\n.date-range {\n  color: var(--text-secondary);\n}\n\n.dialog-footer {\n  background-color: var(--bg-secondary);\n  border-color: var(--border-color);\n}\n\n.btn-cancel {\n  color: var(--text-secondary);\n  border-color: var(--border-color);\n}\n\n.btn-cancel:hover:not(:disabled) {\n  background-color: var(--bg-tertiary);\n  border-color: var(--text-secondary);\n  color: var(--text-primary);\n}\n\n/* Dark Mode Overrides - Only necessary adjustments */\n[data-theme=\"dark\"] .member-section {\n  background-color: var(--bg-tertiary);\n}\n\n[data-theme=\"dark\"] .membership-card {\n  background-color: var(--bg-tertiary);\n}\n\n[data-theme=\"dark\"] .membership-card.selected {\n  background-color: var(--danger-light);\n}\n\n[data-theme=\"dark\"] .dialog-footer {\n  background-color: var(--bg-tertiary);\n}\n\n/* Responsive Design */\n@media screen and (max-width: 768px) {\n  .delete-dialog-container {\n    max-width: 95vw;\n    margin: 0 auto;\n    max-height: 85vh;\n  }\n\n  .dialog-header {\n    padding: 0.75rem 1rem;\n  }\n\n  .dialog-title {\n    font-size: 1.125rem;\n  }\n\n  .dialog-content {\n    padding: 1rem;\n  }\n\n  .member-section {\n    flex-direction: column;\n    text-align: center;\n    gap: 0.75rem;\n  }\n\n  .membership-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n\n  .membership-meta {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n\n  .dialog-footer {\n    flex-direction: column;\n    padding: 1rem;\n    position: sticky;\n    bottom: 0;\n  }\n\n  .btn {\n    width: 100%;\n    min-width: auto;\n  }\n}\n\n@media screen and (max-width: 480px) {\n  .dialog-header {\n    padding: 0.5rem 0.75rem;\n  }\n\n  .dialog-content {\n    padding: 0.75rem;\n  }\n\n  .member-avatar {\n    width: 40px;\n    height: 40px;\n    font-size: 1rem;\n  }\n\n  .package-name {\n    font-size: 1rem;\n  }\n\n  .membership-card {\n    padding: 0.75rem;\n  }\n}\n"], "mappings": ";AACA,CAAC;AACC,SAAO;AACP,aAAW;AACX,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,eAAa;AACb,aAAW,aAAa,KAAK;AAC7B,WAAS;AACT,kBAAgB;AAChB,cAAY;AACd;AAGA,WAPa;AAQX;AACE,aAAS;AACT,eAAW,MAAM,KAAK,WAAW;AACnC;AACA;AACE,aAAS;AACT,eAAW,MAAM,GAAG,WAAW;AACjC;AACF;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS,KAAK;AACd;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,SAAS;AAAA,MAAE;AACnD,SAAO;AACP,iBAAe,IAAI,oBAAoB,IAAI,oBAAoB,EAAE;AACjE,YAAU;AACV,OAAK;AACL,WAAS;AACT,eAAa;AACf;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACb;AAEA,CAAC;AACC,UAAQ;AACR,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,UAAQ;AACR,iBAAe;AACf,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,cAAY,iBAAiB,KAAK;AACpC;AAEA,CAdC,SAcS;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAClC;AAGA,CAAC;AACC,WAAS;AACT,QAAM;AACN,cAAY;AACZ,cAAY;AACd;AAGA,CARC,cAQc;AACb,SAAO;AACT;AAEA,CAZC,cAYc;AACb,cAAY,IAAI;AAChB,iBAAe;AACjB;AAEA,CAjBC,cAiBc;AACb,cAAY,IAAI;AAChB,iBAAe;AACjB;AAEA,CAtBC,cAsBc,yBAAyB;AACtC,cAAY,IAAI;AAClB;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,iBAAe;AACf,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,eAAa,IAAI,MAAM,IAAI;AAC7B;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,eAAa;AACb,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,QAAM;AACR;AAEA,CAAC;AACC,UAAQ,EAAE,EAAE,QAAQ;AACpB,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,UAAQ;AACR,aAAW;AACX,SAAO,IAAI;AACb;AAGA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS;AACT,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,SAAO,IAAI;AACX,aAAW;AACX,eAAa;AACf;AAEA,CAbC,YAaY;AACX,aAAW;AACX,eAAa;AACf;AAGA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,kBAIkB;AACjB,UAAQ;AACR,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACb;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACL,iBAAe;AACjB;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS;AACT,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,UAAQ;AACR,cAAY,IAAI,KAAK;AACvB;AAEA,CAZC,eAYe;AACd,gBAAc,IAAI;AAClB,cAAY,EAAE,IAAI,IAAI,KAAK,IAAI,aAAa,EAAE;AAChD;AAEA,CAjBC,eAiBe,CAAC;AACf,gBAAc,IAAI;AAClB,oBAAkB,IAAI;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,IAAI,aAAa,EAAE;AACjD;AAEA,CAAC;AACC,cAAY;AACd;AAEA,CAJC,iBAIiB,KAAK,CAAC;AACtB,SAAO;AACP,UAAQ;AACR,gBAAc,IAAI;AACpB;AAEA,CAAC;AACC,QAAM;AACN,aAAW;AACb;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe;AACf,OAAK;AACP;AAEA,CAAC;AACC,UAAQ;AACR,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACX,eAAa;AACb,QAAM;AACR;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,WAAS,SAAS;AAClB,iBAAe,IAAI;AACnB,eAAa;AACb,eAAa;AACf;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACD,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,aAAW;AACb;AAEA,CARC;AASC,eAAa;AACf;AAEA,CAZC,eAYe;AAChB,CAZC,WAYW;AACV,SAAO;AACP,cAAY;AACZ,WAAS;AACX;AAGA,CApBC,cAoBc,CAAC;AACd,SAAO,IAAI;AACb;AAEA,CAxBC,cAwBc,CAAC;AACd,SAAO,IAAI;AACb;AAEA,CA5BC,cA4Bc,CAAC;AACd,SAAO,IAAI;AACb;AAEA,CA/BC;AAgCC,SAAO,IAAI;AACb;AAGA,CAAC;AACC,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,OAAxB;AAAA,MAAiC;AAC7C,SAAO;AACP,WAAS,QAAQ;AACjB,iBAAe,IAAI;AACnB,aAAW;AACX,eAAa;AACf;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,OAAK;AACL,WAAS,KAAK;AACd,cAAY,IAAI,MAAM,IAAI;AAC1B,oBAAkB,IAAI;AACtB,iBAAe,EAAE,EAAE,IAAI,oBAAoB,IAAI;AAC/C,YAAU;AACV,UAAQ;AACR,WAAS;AACT,eAAa;AACf;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS,QAAQ;AACjB,UAAQ;AACR,iBAAe,IAAI;AACnB,aAAW;AACX,eAAa;AACb,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,mBAAiB;AACjB,aAAW;AACX,mBAAiB;AACnB;AAEA,CAhBC,IAgBI;AACH,aAAW;AACb;AAEA,CApBC,GAoBG;AACF,WAAS;AACT,UAAQ;AACR,aAAW;AACb;AAEA,CAAC;AACC,oBAAkB;AAClB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CANC,UAMU,MAAM,KAAK;AACpB,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO;AACT;AAEA,CALC,UAKU,MAAM,KAAK;AACpB,oBAAkB,IAAI;AACtB,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,IAAI,aAAa,EAAE;AACjD;AAGA,CAnYC;AAoYC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CA1RC;AA2RC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAnQC;AAoQC,SAAO,IAAI;AACb;AAEA,CAhQC;AAiQC,SAAO,IAAI;AACb;AAEA,CAzPC;AA0PC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CA5OC,kBA4OkB;AACjB,SAAO,IAAI;AACb;AAEA,CA5NC;AA6NC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAlOC,eAkOe,CAjNC;AAkNf,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAxLC;AAyLC,SAAO,IAAI;AACb;AAEA,CAnLC;AAoLC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAtKC;AAuKC,SAAO,IAAI;AACb;AAEA,CAzKC;AA0KC,SAAO,IAAI;AACb;AAEA,CAxHC;AAyHC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CApFC;AAqFC,SAAO,IAAI;AACX,gBAAc,IAAI;AACpB;AAEA,CAzFC,UAyFU,MAAM,KAAK;AACpB,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAGA,CAAC,iBAAmB,CA9VnB;AA+VC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CA7QnB;AA8QC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CAjRnB,eAiRmC,CAhQnB;AAiQf,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CArJnB;AAsJC,oBAAkB,IAAI;AACxB;AAGA,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE;AAC5B,GA9dD;AA+dG,eAAW;AACX,YAAQ,EAAE;AACV,gBAAY;AACd;AAEA,GAzcD;AA0cG,aAAS,QAAQ;AACnB;AAEA,GA9aD;AA+aG,eAAW;AACb;AAEA,GAzZD;AA0ZG,aAAS;AACX;AAEA,GAlYD;AAmYG,oBAAgB;AAChB,gBAAY;AACZ,SAAK;AACP;AAEA,GA5QD;AA6QG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GAtPD;AAuPG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GA/LD;AAgMG,oBAAgB;AAChB,aAAS;AACT,cAAU;AACV,YAAQ;AACV;AAEA,GAvLD;AAwLG,WAAO;AACP,eAAW;AACb;AACF;AAEA,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE;AAC5B,GArfD;AAsfG,aAAS,OAAO;AAClB;AAEA,GAjcD;AAkcG,aAAS;AACX;AAEA,GA/ZD;AAgaG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GA5SD;AA6SG,eAAW;AACb;AAEA,GA/VD;AAgWG,aAAS;AACX;AACF;", "names": []}