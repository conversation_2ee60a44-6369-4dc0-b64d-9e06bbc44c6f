# 🚀 GymKod Deployment Guide - Basit Kullanım

## 📍 **ÖNEMLİ: Script'leri NEREDE çalıştıracaksın?**

**Ana klasörden çalıştır!** (GymProject klasöründen)

```
C:\Users\<USER>\Desktop\GymProject\  ← BURADAN çalıştır!
├── GymProjectBackend/
├── GymProjectFrontend/
├── deploy-development.ps1          ← Bu script'leri kullan
├── deploy-staging.ps1
└── deploy-production.ps1
```

## 🎯 **HIZLI KULLANIM**

### **1. Development (Geliştirme)**
```powershell
# Terminal'i aç
cd C:\Users\<USER>\Desktop\GymProject

# Development script'ini çalıştır
.\deploy-development.ps1

# Sonuç: localhost:5165'te Development environment hazır!
```

### **2. Staging (Test)**
```powershell
# Terminal'i aç
cd C:\Users\<USER>\Desktop\GymProject

# Staging script'ini çalıştır
.\deploy-staging.ps1

# Sonuç: staging.gymkod.com için hazır!
```

### **3. Production (Canlı)**
```powershell
# Terminal'i aç
cd C:\Users\<USER>\Desktop\GymProject

# Production script'ini çalıştır (DİKKATLİ!)
.\deploy-production.ps1

# Onay: "DEPLOY-PRODUCTION" yazman gerekecek
# Sonuç: admin.gymkod.com için hazır!
```

## 🔧 **SCRIPT PARAMETRELERI**

### **Sadece Backend Build Et:**
```powershell
.\deploy-development.ps1 -SkipFrontend
```

### **Sadece Frontend Build Et:**
```powershell
.\deploy-development.ps1 -SkipBuild
```

### **Hiçbirini Build Etme (Sadece Environment Set Et):**
```powershell
.\deploy-development.ps1 -SkipBuild -SkipFrontend
```

## 🎮 **ADIM ADIM İLK KULLANIM**

### **Test Edelim:**

1. **Terminal Aç:**
   ```powershell
   # PowerShell'i Administrator olarak aç
   cd C:\Users\<USER>\Desktop\GymProject
   ```

2. **Development Script'ini Çalıştır:**
   ```powershell
   .\deploy-development.ps1
   ```

3. **Ne Olacak:**
   ```
   === GymKod Development Deployment ===
   ✅ Environment set to: Development
   ✅ Backend build successful!
   ✅ Frontend build successful!
   🚀 Ready to code! Happy Development! 🚀
   ```

4. **Uygulamayı Başlat:**
   ```powershell
   # Backend
   cd GymProjectBackend
   dotnet run --project WebAPI

   # Yeni terminal aç, Frontend
   cd GymProjectFrontend  
   ng serve
   ```

5. **Test Et:**
   - Backend: http://localhost:5165/api/
   - Frontend: http://localhost:4200

## ❓ **SORUN GİDERME**

### **Problem: "execution policy" hatası**
```powershell
# Çözüm:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### **Problem: "ng command not found"**
```powershell
# Çözüm:
npm install -g @angular/cli
```

### **Problem: "dotnet command not found"**
- .NET 8 SDK'yı yükle: https://dotnet.microsoft.com/download

### **Problem: Script çalışmıyor**
```powershell
# Doğru klasörde olduğunu kontrol et:
pwd
# Çıktı: C:\Users\<USER>\Desktop\GymProject olmalı

# Script'in var olduğunu kontrol et:
ls *.ps1
```

## 🎊 **BAŞARILI DEPLOYMENT SONRASI**

### **Development:**
- ✅ Database: GymProject (Local)
- ✅ API: http://localhost:5165/api/
- ✅ Frontend: http://localhost:4200
- ✅ Swagger: Açık

### **Staging:**
- ✅ Database: Staging
- ✅ API: https://staging.gymkod.com/api/
- ✅ Frontend: https://staging.gymkod.com
- ✅ Swagger: Açık

### **Production:**
- ✅ Database: GymProject (LIVE)
- ✅ API: https://admin.gymkod.com/api/
- ✅ Frontend: https://admin.gymkod.com
- ❌ Swagger: Kapalı (Güvenlik)

## 🚀 **ÖZET**

**Artık sadece 3 komut bilmen yeterli:**

```powershell
.\deploy-development.ps1    # Geliştirme
.\deploy-staging.ps1        # Test
.\deploy-production.ps1     # Canlı
```

**Hiç kod değiştirmeyeceksin! 🎉**
